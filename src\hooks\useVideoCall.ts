import { useState, useEffect, useCallback, useRef } from 'react';
import VideoCallService, {
  CallSession,
  CallInvitation
} from '@/services/VideoCallService';
import { toast } from 'sonner';

interface UseVideoCallReturn {
  // State
  currentCall: CallSession | null;
  incomingCall: CallInvitation | null;
  outgoingCall: {
    recipientName: string;
    recipientId: string;
    callType: 'video' | 'audio';
  } | null;
  isConnected: boolean;
  isInitialized: boolean;
  
  // Actions
  startCall: (participantIds: string[], type?: 'video' | 'audio') => Promise<string>;
  acceptCall: (invitation: CallInvitation) => Promise<void>;
  rejectCall: (invitation: CallInvitation) => void;
  endCall: () => void;
  toggleVideo: () => boolean;
  toggleAudio: () => boolean;
  toggleScreenShare: () => Promise<void>;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  sendChatMessage: (message: string) => void;
  
  // Utilities
  isInCall: () => boolean;
  getCallDuration: () => number;
  getParticipantCount: () => number;
}

export const useVideoCall = (): UseVideoCallReturn => {
  const [currentCall, setCurrentCall] = useState<CallSession | null>(null);
  const [incomingCall, setIncomingCall] = useState<CallInvitation | null>(null);
  const [outgoingCall, setOutgoingCall] = useState<{
    recipientName: string;
    recipientId: string;
    callType: 'video' | 'audio';
  } | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  const videoCallService = useRef(VideoCallService.getInstance());
  const callStartTime = useRef<Date | null>(null);

  // Action handlers (defined early to avoid hoisting issues)
  const acceptCall = useCallback(async (invitation: CallInvitation): Promise<void> => {
    try {
      await videoCallService.current.acceptCall(invitation);
      setIncomingCall(null);
    } catch (error) {
      toast.error('Failed to accept call');
      throw error;
    }
  }, []);

  const rejectCall = useCallback((invitation: CallInvitation): void => {
    videoCallService.current.rejectCall(invitation);
    setIncomingCall(null);
  }, []);

  // Initialize service and event listeners
  useEffect(() => {
    const service = videoCallService.current;

    const handleConnected = () => {
      setIsConnected(true);
      setIsInitialized(true);
    };

    const handleDisconnected = () => {
      setIsConnected(false);
    };

    const handleCallInvitation = (invitation: CallInvitation) => {
      setIncomingCall(invitation);
      
      // Show notification
      toast.info(`Incoming ${invitation.type} call from ${invitation.fromUserName}`, {
        duration: 30000,
        action: {
          label: 'Answer',
          onClick: () => acceptCall(invitation)
        }
      });
    };

    const handleCallStarted = (session: CallSession) => {
      setCurrentCall(session);
      setOutgoingCall(null);
      callStartTime.current = session.startTime;
      toast.success('Call started');
    };

    const handleCallAccepted = (session: CallSession) => {
      setCurrentCall(session);
      setIncomingCall(null);
      setOutgoingCall(null);
      callStartTime.current = session.startTime;
      toast.success('Call connected');
    };

    const handleCallRejected = () => {
      setOutgoingCall(null);
      toast.info('Call was rejected');
    };

    const handleCallEnded = (session: CallSession) => {
      setCurrentCall(null);
      setIncomingCall(null);
      setOutgoingCall(null);
      callStartTime.current = null;
      
      const duration = session.startTime 
        ? Math.floor((Date.now() - session.startTime.getTime()) / 1000)
        : 0;
      
      toast.info(`Call ended. Duration: ${formatDuration(duration)}`);
    };

    const handleParticipantJoined = (data: { userId: string; userName: string }) => {
      toast.info(`${data.userName} joined the call`);
    };

    const handleParticipantLeft = (data: { userId: string; userName: string }) => {
      toast.info(`${data.userName} left the call`);
    };

    const handleMediaToggled = (data: { 
      userId: string; 
      mediaType: 'video' | 'audio' | 'screen'; 
      enabled: boolean 
    }) => {
      // Update UI based on media toggle
      if (currentCall) {
        const participant = currentCall.participants.find(p => p.id === data.userId);
        if (participant) {
          switch (data.mediaType) {
            case 'video':
              participant.isVideoEnabled = data.enabled;
              break;
            case 'audio':
              participant.isAudioEnabled = data.enabled;
              break;
            case 'screen':
              participant.isScreenSharing = data.enabled;
              break;
          }
          setCurrentCall({ ...currentCall });
        }
      }
    };

    const handleRecordingStarted = () => {
      toast.success('Call recording started');
    };

    const handleRecordingStopped = (url: string) => {
      toast.success('Call recording saved', {
        action: {
          label: 'Download',
          onClick: () => {
            const a = document.createElement('a');
            a.href = url;
            a.download = `call-recording-${Date.now()}.webm`;
            a.click();
          }
        }
      });
    };

    // Subscribe to events
    service.on('connected', handleConnected);
    service.on('disconnected', handleDisconnected);
    service.on('call-invitation', handleCallInvitation);
    service.on('call-started', handleCallStarted);
    service.on('call-accepted', handleCallAccepted);
    service.on('call-rejected', handleCallRejected);
    service.on('call-ended', handleCallEnded);
    service.on('participant-joined', handleParticipantJoined);
    service.on('participant-left', handleParticipantLeft);
    service.on('media-toggled', handleMediaToggled);
    service.on('recording-started', handleRecordingStarted);
    service.on('recording-stopped', handleRecordingStopped);

    // Check initial connection state
    if (service.isInCall()) {
      setCurrentCall(service.getCurrentCall());
    }

    return () => {
      // Cleanup event listeners
      service.off('connected', handleConnected);
      service.off('disconnected', handleDisconnected);
      service.off('call-invitation', handleCallInvitation);
      service.off('call-started', handleCallStarted);
      service.off('call-accepted', handleCallAccepted);
      service.off('call-rejected', handleCallRejected);
      service.off('call-ended', handleCallEnded);
      service.off('participant-joined', handleParticipantJoined);
      service.off('participant-left', handleParticipantLeft);
      service.off('media-toggled', handleMediaToggled);
      service.off('recording-started', handleRecordingStarted);
      service.off('recording-stopped', handleRecordingStopped);
    };
  }, [currentCall, acceptCall]);

  // Helper function to format duration
  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Action handlers
  const startCall = useCallback(async (
    participantIds: string[], 
    type: 'video' | 'audio' = 'video'
  ): Promise<string> => {
    try {
      // For demo purposes, we'll use mock participant names
      const mockParticipantNames = [
        'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson'
      ];
      
      if (participantIds.length === 1) {
        setOutgoingCall({
          recipientName: mockParticipantNames[0],
          recipientId: participantIds[0],
          callType: type
        });
      }

      const callId = await videoCallService.current.startCall(participantIds, type);
      return callId;
    } catch (error) {
      toast.error('Failed to start call');
      throw error;
    }
  }, []);

  const endCall = useCallback((): void => {
    videoCallService.current.endCall();
  }, []);

  const toggleVideo = useCallback((): boolean => {
    return videoCallService.current.toggleVideo();
  }, []);

  const toggleAudio = useCallback((): boolean => {
    return videoCallService.current.toggleAudio();
  }, []);

  const toggleScreenShare = useCallback(async (): Promise<void> => {
    const service = videoCallService.current;
    if (service.getScreenStream()) {
      service.stopScreenShare();
    } else {
      await service.startScreenShare();
    }
  }, []);

  const startRecording = useCallback(async (): Promise<void> => {
    await videoCallService.current.startRecording();
  }, []);

  const stopRecording = useCallback((): void => {
    videoCallService.current.stopRecording();
  }, []);

  const sendChatMessage = useCallback((message: string): void => {
    videoCallService.current.sendChatMessage(message);
  }, []);

  // Utility functions
  const isInCall = useCallback((): boolean => {
    return videoCallService.current.isInCall();
  }, []);

  const getCallDuration = useCallback((): number => {
    if (callStartTime.current) {
      return Math.floor((Date.now() - callStartTime.current.getTime()) / 1000);
    }
    return 0;
  }, []);

  const getParticipantCount = useCallback((): number => {
    return currentCall?.participants.length || 0;
  }, [currentCall]);

  return {
    // State
    currentCall,
    incomingCall,
    outgoingCall,
    isConnected,
    isInitialized,
    
    // Actions
    startCall,
    acceptCall,
    rejectCall,
    endCall,
    toggleVideo,
    toggleAudio,
    toggleScreenShare,
    startRecording,
    stopRecording,
    sendChatMessage,
    
    // Utilities
    isInCall,
    getCallDuration,
    getParticipantCount
  };
};
