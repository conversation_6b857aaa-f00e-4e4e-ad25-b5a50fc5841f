import React from 'react';
import { User, UserPlus, Setting<PERSON>, MoreHorizontal } from 'lucide-react';

interface Profile {
  id: string;
  name: string;
  avatar: string;
  role: string;
  status: 'active' | 'away' | 'busy' | 'offline';
  mutualFriends?: number;
  isCurrentUser?: boolean;
}

const ProfilesWidget: React.FC = () => {
  const profiles: Profile[] = [
    {
      id: '1',
      name: 'You',
      avatar: '/placeholder.svg',
      role: 'Your Profile',
      status: 'active',
      isCurrentUser: true,
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      role: 'Software Engineer',
      status: 'active',
      mutualFriends: 12,
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      role: 'Product Designer',
      status: 'away',
      mutualFriends: 8,
    },
    {
      id: '4',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      role: 'Marketing Manager',
      status: 'busy',
      mutualFriends: 15,
    },
    {
      id: '5',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      role: 'Data Scientist',
      status: 'offline',
      mutualFriends: 6,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-500';
      case 'away':
        return 'bg-yellow-500';
      case 'busy':
        return 'bg-red-500';
      case 'offline':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Active';
      case 'away':
        return 'Away';
      case 'busy':
        return 'Busy';
      case 'offline':
        return 'Offline';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <User className="w-4 h-4" />
          Profiles
        </h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          See All
        </button>
      </div>
      
      <div className="space-y-3">
        {profiles.map((profile) => (
          <div key={profile.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer group">
            <div className="relative">
              <img
                src={profile.avatar}
                alt={profile.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 ${getStatusColor(profile.status)} border-2 border-white dark:border-gray-800 rounded-full`}></div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {profile.name}
                  {profile.isCurrentUser && (
                    <span className="ml-1 text-xs text-blue-600 font-normal">(You)</span>
                  )}
                </p>
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {!profile.isCurrentUser && (
                    <button className="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors">
                      <UserPlus className="w-3 h-3" />
                    </button>
                  )}
                  <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                    {profile.isCurrentUser ? <Settings className="w-3 h-3" /> : <MoreHorizontal className="w-3 h-3" />}
                  </button>
                </div>
              </div>
              
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {profile.role}
              </p>
              
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center gap-2">
                  <span className={`text-xs ${profile.status === 'active' ? 'text-green-600' : profile.status === 'away' ? 'text-yellow-600' : profile.status === 'busy' ? 'text-red-600' : 'text-gray-500'}`}>
                    {getStatusText(profile.status)}
                  </span>
                  {profile.mutualFriends && (
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      • {profile.mutualFriends} mutual friends
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full mt-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors flex items-center justify-center gap-2">
        <UserPlus className="w-4 h-4" />
        Find More People
      </button>
    </div>
  );
};

export default ProfilesWidget;