import React, { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Initialize global error handler
import './utils/errorHandler';
import AppErrorBoundary from './components/ui/AppErrorBoundary.tsx';
import AppSuspenseFallback from './components/ui/AppSuspenseFallback.tsx';

// Create root and render app
const rootElement = document.getElementById("root");
if (!rootElement) throw new Error("Failed to find the root element");

createRoot(rootElement).render(
  <AppErrorBoundary>
    <Suspense fallback={<AppSuspenseFallback />}>
      <App />
    </Suspense>
  </AppErrorBoundary>
);
