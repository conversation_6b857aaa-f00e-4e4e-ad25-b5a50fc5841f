import React from 'react';
import { MessageSquare, Users, MoreHorizontal } from 'lucide-react';

interface GroupConversation {
  id: string;
  name: string;
  avatar: string;
  lastMessage: string;
  lastMessageTime: string;
  memberCount: number;
  unreadCount?: number;
  isActive: boolean;
}

const GroupConversationsWidget: React.FC = () => {
  const groupConversations: GroupConversation[] = [
    {
      id: '1',
      name: 'Family Group',
      avatar: '/placeholder.svg',
      lastMessage: 'Mom: Don\'t forget dinner tonight!',
      lastMessageTime: '2m',
      memberCount: 5,
      unreadCount: 3,
      isActive: true,
    },
    {
      id: '2',
      name: 'Work Team',
      avatar: '/placeholder.svg',
      lastMessage: 'John: Meeting at 3 PM',
      lastMessageTime: '15m',
      memberCount: 8,
      unreadCount: 1,
      isActive: true,
    },
    {
      id: '3',
      name: 'College Friends',
      avatar: '/placeholder.svg',
      lastMessage: '<PERSON>: Anyone up for weekend trip?',
      lastMessageTime: '1h',
      memberCount: 12,
      isActive: false,
    },
    {
      id: '4',
      name: 'Book Club',
      avatar: '/placeholder.svg',
      lastMessage: 'Emma: Finished chapter 5',
      lastMessageTime: '3h',
      memberCount: 6,
      isActive: false,
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <MessageSquare className="w-4 h-4" />
          Group Conversations
        </h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          See All
        </button>
      </div>
      
      <div className="space-y-3">
        {groupConversations.map((group) => (
          <div key={group.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer group">
            <div className="relative">
              <img
                src={group.avatar}
                alt={group.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              {group.isActive && (
                <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {group.name}
                </p>
                <div className="flex items-center gap-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {group.lastMessageTime}
                  </span>
                  {group.unreadCount && (
                    <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center">
                      {group.unreadCount}
                    </span>
                  )}
                </div>
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <p className="text-xs text-gray-500 dark:text-gray-400 truncate flex-1">
                  {group.lastMessage}
                </p>
              </div>
              
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <Users className="w-3 h-3" />
                  <span>{group.memberCount} members</span>
                </div>
                <button className="opacity-0 group-hover:opacity-100 p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 rounded transition-all">
                  <MoreHorizontal className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full mt-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors">
        Create New Group
      </button>
    </div>
  );
};

export default GroupConversationsWidget;