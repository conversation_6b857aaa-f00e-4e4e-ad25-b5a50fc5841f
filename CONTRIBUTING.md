# Contributing Guidelines

Thank you for considering contributing to our project! Please read this document carefully to understand our contribution guidelines.

## Linting Rules

To maintain code consistency and quality, our project uses ESLint with specific rules. An important rule to note:

- **Prefix Unused Parameters**: If a function parameter is unused, prefix it with an underscore (`_`). For example:
  ```typescript
  function example(_unusedParam: string) {
    // function logic here
  }
  ```

This helps avoid ESLint warnings about unused variables while clearly indicating intentional non-use.

## Development Workflow
- Fork the repository and clone the fork.
- Create a new branch for each feature or fix.
- Write clear, concise commit messages following conventional commit standards.
- Make sure to write tests for new features or bug fixes.

## Code Quality
- Run `npm run lint` and ensure all linting issues are resolved.
- Run `npm run test` and ensure all tests pass.

We appreciate your contributions and will do our best to review your pull requests promptly!
