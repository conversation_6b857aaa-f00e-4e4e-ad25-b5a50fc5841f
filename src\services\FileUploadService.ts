// Browser-compatible EventEmitter implementation
class EventEmitter {
  private events: { [key: string]: ((...args: unknown[]) => void)[] } = {};

  on(event: string, listener: (...args: unknown[]) => void): this {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(listener);
    return this;
  }

  emit(event: string, ...args: unknown[]): boolean {
    if (!this.events[event]) {
      return false;
    }
    this.events[event].forEach(listener => {
      listener(...args);
    });
    return true;
  }

  removeAllListeners(event?: string): this {
    if (event) {
      delete this.events[event];
    } else {
      this.events = {};
    }
    return this;
  }

  off(event: string, listener: (...args: unknown[]) => void): this {
    if (!this.events[event]) {
      return this;
    }
    this.events[event] = this.events[event].filter(l => l !== listener);
    return this;
  }
}

export interface UploadFile {
  id: string;
  file: File;
  name: string;
  size: number;
  type: string;
  preview?: string;
  thumbnail?: string;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error' | 'cancelled';
  error?: string;
  url?: string;
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
    bitrate?: number;
    format?: string;
    compressed?: boolean;
    originalSize?: number;
  };
}

export interface UploadOptions {
  maxFileSize?: number; // in bytes
  allowedTypes?: string[];
  maxFiles?: number;
  autoCompress?: boolean;
  compressionQuality?: number; // 0-1
  generateThumbnails?: boolean;
  thumbnailSize?: { width: number; height: number };
  chunkSize?: number; // for chunked uploads
  retryAttempts?: number;
  onProgress?: (file: UploadFile, progress: number) => void;
  onComplete?: (file: UploadFile) => void;
  onError?: (file: UploadFile, error: string) => void;
}

class FileUploadService extends EventEmitter {
  private static instance: FileUploadService;
  private uploads: Map<string, UploadFile> = new Map();
  private activeUploads = 0;
  private maxConcurrentUploads = 3;

  // Default options
  private defaultOptions: UploadOptions = {
    maxFileSize: 100 * 1024 * 1024, // 100MB
    allowedTypes: ['image/*', 'video/*', 'audio/*', 'application/pdf', 'text/*'],
    maxFiles: 10,
    autoCompress: true,
    compressionQuality: 0.8,
    generateThumbnails: true,
    thumbnailSize: { width: 300, height: 300 },
    chunkSize: 1024 * 1024, // 1MB chunks
    retryAttempts: 3
  };

  static getInstance(): FileUploadService {
    if (!FileUploadService.instance) {
      FileUploadService.instance = new FileUploadService();
    }
    return FileUploadService.instance;
  }

  constructor() {
    super();
  }

  // Validate files before upload
  validateFiles(files: FileList | File[], options: UploadOptions = {}): { valid: File[]; invalid: { file: File; reason: string }[] } {
    const opts = { ...this.defaultOptions, ...options };
    const valid: File[] = [];
    const invalid: { file: File; reason: string }[] = [];

    Array.from(files).forEach(file => {
      // Check file size
      if (opts.maxFileSize && file.size > opts.maxFileSize) {
        invalid.push({ file, reason: `File size exceeds ${this.formatFileSize(opts.maxFileSize)}` });
        return;
      }

      // Check file type
      if (opts.allowedTypes && !this.isFileTypeAllowed(file, opts.allowedTypes)) {
        invalid.push({ file, reason: 'File type not allowed' });
        return;
      }

      valid.push(file);
    });

    // Check max files limit
    if (opts.maxFiles && valid.length > opts.maxFiles) {
      const excess = valid.splice(opts.maxFiles);
      excess.forEach(file => {
        invalid.push({ file, reason: `Maximum ${opts.maxFiles} files allowed` });
      });
    }

    return { valid, invalid };
  }

  private isFileTypeAllowed(file: File, allowedTypes: string[]): boolean {
    return allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        const category = type.slice(0, -2);
        return file.type.startsWith(category);
      }
      return file.type === type;
    });
  }

  // Upload files
  async uploadFiles(files: FileList | File[], options: UploadOptions = {}): Promise<UploadFile[]> {
    const opts = { ...this.defaultOptions, ...options };
    const { valid, invalid } = this.validateFiles(files, opts);

    // Emit validation errors
    invalid.forEach(({ file, reason }) => {
      this.emit('validation_error', { file, reason });
    });

    if (valid.length === 0) {
      throw new Error('No valid files to upload');
    }

    const uploadFiles: UploadFile[] = [];

    for (const file of valid) {
      const uploadFile = await this.createUploadFile(file, opts);
      uploadFiles.push(uploadFile);
      this.uploads.set(uploadFile.id, uploadFile);
    }

    // Start uploads
    uploadFiles.forEach(uploadFile => {
      this.startUpload(uploadFile, opts);
    });

    return uploadFiles;
  }

  private async createUploadFile(file: File, options: UploadOptions): Promise<UploadFile> {
    const id = `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const uploadFile: UploadFile = {
      id,
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'pending'
    };

    // Generate preview for images
    if (file.type.startsWith('image/')) {
      uploadFile.preview = await this.generatePreview(file);
      
      if (options.generateThumbnails) {
        uploadFile.thumbnail = await this.generateThumbnail(file, options.thumbnailSize!);
      }

      // Get image metadata
      const metadata = await this.getImageMetadata(file);
      uploadFile.metadata = metadata;
    }

    // Generate preview for videos
    if (file.type.startsWith('video/')) {
      uploadFile.preview = await this.generateVideoPreview(file);
      
      // Get video metadata
      const metadata = await this.getVideoMetadata(file);
      uploadFile.metadata = metadata;
    }

    return uploadFile;
  }

  private async generatePreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  private async generateThumbnail(file: File, size: { width: number; height: number }): Promise<string> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate aspect ratio
        const aspectRatio = img.width / img.height;
        let { width, height } = size;

        if (aspectRatio > 1) {
          height = width / aspectRatio;
        } else {
          width = height * aspectRatio;
        }

        canvas.width = width;
        canvas.height = height;

        ctx?.drawImage(img, 0, 0, width, height);
        resolve(canvas.toDataURL('image/jpeg', 0.8));
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  private async generateVideoPreview(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.onloadedmetadata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        video.currentTime = Math.min(1, video.duration / 2); // Seek to middle or 1 second
      };

      video.onseeked = () => {
        ctx?.drawImage(video, 0, 0);
        resolve(canvas.toDataURL('image/jpeg', 0.8));
        URL.revokeObjectURL(video.src);
      };

      video.onerror = reject;
      video.src = URL.createObjectURL(file);
    });
  }

  private async getImageMetadata(file: File): Promise<{ width: number; height: number; aspectRatio: number; size: number; format?: string; originalSize?: number }> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.width,
          height: img.height,
          aspectRatio: img.width / img.height,
          size: file.size,
          format: file.type,
          originalSize: file.size
        });
        URL.revokeObjectURL(img.src);
      };
      img.onerror = () => resolve({
        width: 0,
        height: 0,
        aspectRatio: 1,
        size: file.size
      });
      img.src = URL.createObjectURL(file);
    });
  }

  private async getVideoMetadata(file: File): Promise<{ width: number; height: number; duration: number; aspectRatio: number; size: number; format?: string; originalSize?: number }> {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve({
          width: video.videoWidth,
          height: video.videoHeight,
          duration: video.duration,
          aspectRatio: video.videoWidth / video.videoHeight,
          size: file.size,
          format: file.type,
          originalSize: file.size
        });
        URL.revokeObjectURL(video.src);
      };
      video.onerror = () => resolve({
        width: 0,
        height: 0,
        duration: 0,
        aspectRatio: 1,
        size: file.size
      });
      video.src = URL.createObjectURL(file);
    });
  }

  private async startUpload(uploadFile: UploadFile, options: UploadOptions) {
    if (this.activeUploads >= this.maxConcurrentUploads) {
      // Queue the upload
      setTimeout(() => this.startUpload(uploadFile, options), 1000);
      return;
    }

    this.activeUploads++;
    uploadFile.status = 'uploading';
    this.emit('upload_started', uploadFile);

    try {
      // Compress file if needed
      let fileToUpload = uploadFile.file;
      if (options.autoCompress && uploadFile.file.type.startsWith('image/')) {
        fileToUpload = await this.compressImage(uploadFile.file, options.compressionQuality!);
        uploadFile.metadata = { ...uploadFile.metadata, compressed: true };
      }

      // Simulate chunked upload
      await this.performChunkedUpload(uploadFile, fileToUpload, options);

      uploadFile.status = 'completed';
      uploadFile.progress = 100;
      uploadFile.url = `https://example.com/uploads/${uploadFile.id}`;
      
      this.emit('upload_completed', uploadFile);
      options.onComplete?.(uploadFile);

    } catch (error) {
      uploadFile.status = 'error';
      uploadFile.error = error instanceof Error ? error.message : 'Upload failed';
      
      this.emit('upload_error', uploadFile);
      options.onError?.(uploadFile, uploadFile.error);
    } finally {
      this.activeUploads--;
    }
  }

  private async compressImage(file: File, quality: number): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions (max 1920x1080)
        const maxWidth = 1920;
        const maxHeight = 1080;
        let { width, height } = img;

        if (width > maxWidth || height > maxHeight) {
          const aspectRatio = width / height;
          if (width > height) {
            width = maxWidth;
            height = width / aspectRatio;
          } else {
            height = maxHeight;
            width = height * aspectRatio;
          }
        }

        canvas.width = width;
        canvas.height = height;
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Compression failed'));
            }
          },
          'image/jpeg',
          quality
        );
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  private async performChunkedUpload(uploadFile: UploadFile, file: File, options: UploadOptions): Promise<void> {
    const chunkSize = options.chunkSize!;
    const totalChunks = Math.ceil(file.size / chunkSize);
    
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      file.slice(start, end);

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));

      // Update progress
      const progress = Math.round(((chunkIndex + 1) / totalChunks) * 100);
      uploadFile.progress = progress;
      
      this.emit('upload_progress', uploadFile);
      options.onProgress?.(uploadFile, progress);

      // Simulate random failures for demo
      if (Math.random() < 0.05) { // 5% chance of failure
        throw new Error('Network error during upload');
      }
    }
  }

  // Cancel upload
  cancelUpload(uploadId: string): void {
    const uploadFile = this.uploads.get(uploadId);
    if (uploadFile && uploadFile.status === 'uploading') {
      uploadFile.status = 'cancelled';
      this.emit('upload_cancelled', uploadFile);
    }
  }

  // Retry upload
  async retryUpload(uploadId: string, options: UploadOptions = {}): Promise<void> {
    const uploadFile = this.uploads.get(uploadId);
    if (uploadFile && uploadFile.status === 'error') {
      uploadFile.status = 'pending';
      uploadFile.progress = 0;
      uploadFile.error = undefined;
      await this.startUpload(uploadFile, { ...this.defaultOptions, ...options });
    }
  }

  // Get upload status
  getUpload(uploadId: string): UploadFile | undefined {
    return this.uploads.get(uploadId);
  }

  // Get all uploads
  getAllUploads(): UploadFile[] {
    return Array.from(this.uploads.values());
  }

  // Clear completed uploads
  clearCompleted(): void {
    for (const [id, upload] of this.uploads) {
      if (upload.status === 'completed') {
        this.uploads.delete(id);
      }
    }
  }

  // Utility methods
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFormattedFileSize(uploadId: string): string {
    const upload = this.uploads.get(uploadId);
    return upload ? this.formatFileSize(upload.size) : '';
  }

  // Cleanup
  destroy(): void {
    this.uploads.clear();
    this.removeAllListeners();
  }
}

export default FileUploadService;
