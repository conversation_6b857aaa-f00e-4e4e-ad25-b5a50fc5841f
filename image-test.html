<!DOCTYPE html>
<html>
<head>
    <title>Direct Image Test</title>
</head>
<body>
    <h1>Direct Image Loading Test</h1>
    <div style="margin: 20px 0;">
        <h3>Test Image 1:</h3>
        <img src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop" 
             style="max-width: 400px; border: 2px solid #ccc;" 
             onload="console.log('✅ Image 1 loaded'); document.getElementById('status1').textContent = 'LOADED'"
             onerror="console.log('❌ Image 1 failed'); document.getElementById('status1').textContent = 'FAILED'">
        <div id="status1">Loading...</div>
    </div>
    
    <div style="margin: 20px 0;">
        <h3>Test Image 2:</h3>
        <img src="https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?w=800&h=600&fit=crop" 
             style="max-width: 400px; border: 2px solid #ccc;" 
             onload="console.log('✅ Image 2 loaded'); document.getElementById('status2').textContent = 'LOADED'"
             onerror="console.log('❌ Image 2 failed'); document.getElementById('status2').textContent = 'FAILED'">
        <div id="status2">Loading...</div>
    </div>
    
    <script>
        console.log('Testing direct image loading...');
        
        // Test fetch to check network
        fetch('https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop')
            .then(response => {
                console.log('Fetch test - Status:', response.status);
                console.log('Fetch test - Headers:', response.headers.get('content-type'));
            })
            .catch(error => {
                console.error('Fetch test failed:', error);
            });
    </script>
</body>
</html>
