import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, act } from '@testing-library/react'
import { ThemeProvider } from '@/contexts/ThemeProvider'
import { ThemeContext } from '@/contexts/ThemeContext'
import { useContext } from 'react'

// Mock storage
const mockStorage = {
  get: vi.fn(),
  set: vi.fn()
}

vi.mock('@/lib/storage', () => ({
  storage: mockStorage
}))

vi.mock('@/lib/constants', () => ({
  STORAGE_KEYS: {
    THEME: 'theme'
  }
}))

// Test component that uses ThemeContext
const TestThemeConsumer = () => {
  const { theme, setTheme } = useContext(ThemeContext)
  
  return (
    <div>
      <span data-testid="current-theme">{theme}</span>
      <button onClick={() => setTheme('dark')} data-testid="set-dark">
        Set Dark
      </button>
      <button onClick={() => setTheme('light')} data-testid="set-light">
        Set Light
      </button>
      <button onClick={() => setTheme('system')} data-testid="set-system">
        Set System
      </button>
    </div>
  )
}

// Mock document and window objects
const mockMatchMedia = vi.fn()
const mockDocumentElement = {
  classList: {
    add: vi.fn(),
    remove: vi.fn()
  }
}

const mockMetaElement = {
  setAttribute: vi.fn()
}

describe('ThemeProvider and ThemeContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockStorage.get.mockReturnValue(null)
    
    // Mock window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: mockMatchMedia.mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }))
    })
    
    // Mock document.documentElement
    Object.defineProperty(document, 'documentElement', {
      writable: true,
      value: mockDocumentElement
    })
    
    // Mock querySelector for meta theme-color
    vi.spyOn(document, 'querySelector').mockReturnValue(mockMetaElement as Element | null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it('provides theme context to children', () => {
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(screen.getByTestId('current-theme')).toBeInTheDocument()
  })

  it('initializes with saved theme from storage', () => {
    mockStorage.get.mockReturnValue('dark')
    
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
  })

  it('initializes with system preference when no saved theme', () => {
    mockMatchMedia.mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
  })

  it('defaults to light theme when no storage or system preference', () => {
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(screen.getByTestId('current-theme')).toHaveTextContent('light')
  })

  it('updates theme when setTheme is called', () => {
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    act(() => {
      screen.getByTestId('set-dark').click()
    })
    
    expect(screen.getByTestId('current-theme')).toHaveTextContent('dark')
    expect(mockStorage.set).toHaveBeenCalledWith('theme', 'dark')
  })

  it('applies theme class to document element', () => {
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    act(() => {
      screen.getByTestId('set-dark').click()
    })
    
    expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('light', 'dark')
    expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
  })

  it('handles system theme correctly', () => {
    mockMatchMedia.mockImplementation(query => ({
      matches: query === '(prefers-color-scheme: dark)',
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    act(() => {
      screen.getByTestId('set-system').click()
    })
    
    expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('dark')
  })

  it('updates meta theme-color when theme changes', () => {
    render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    act(() => {
      screen.getByTestId('set-dark').click()
    })
    
    expect(mockMetaElement.setAttribute).toHaveBeenCalledWith('content', '#1f2937')
    
    act(() => {
      screen.getByTestId('set-light').click()
    })
    
    expect(mockMetaElement.setAttribute).toHaveBeenCalledWith('content', '#ffffff')
  })

  it('throws error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})
    
    expect(() => {
      render(<TestThemeConsumer />)
    }).toThrow('useTheme must be used within a ThemeProvider')
    
    consoleSpy.mockRestore()
  })

  it('listens for system theme changes', () => {
    const mockAddEventListener = vi.fn()
    const mockRemoveEventListener = vi.fn()
    
    mockMatchMedia.mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: mockAddEventListener,
      removeEventListener: mockRemoveEventListener,
      dispatchEvent: vi.fn(),
    }))
    
    const { unmount } = render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(mockAddEventListener).toHaveBeenCalledWith('change', expect.any(Function))
    
    unmount()
    
    expect(mockRemoveEventListener).toHaveBeenCalledWith('change', expect.any(Function))
  })

  it('falls back to legacy listeners for older browsers', () => {
    const mockAddListener = vi.fn()
    const mockRemoveListener = vi.fn()
    const mockAddEventListener = vi.fn().mockImplementation(() => {
      throw new Error('Not supported')
    })
    
    mockMatchMedia.mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: mockAddListener,
      removeListener: mockRemoveListener,
      addEventListener: mockAddEventListener,
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    }))
    
    const { unmount } = render(
      <ThemeProvider>
        <TestThemeConsumer />
      </ThemeProvider>
    )
    
    expect(mockAddListener).toHaveBeenCalledWith(expect.any(Function))
    
    unmount()
    
    expect(mockRemoveListener).toHaveBeenCalledWith(expect.any(Function))
  })
})
