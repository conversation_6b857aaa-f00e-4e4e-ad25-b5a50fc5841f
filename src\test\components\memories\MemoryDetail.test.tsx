import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import MemoryDetail from '@/components/memories/MemoryDetail';
import { MOCK_IMAGES } from '@/lib/constants';
import { toast } from 'sonner';

// Mock dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn()
  }
}));

vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.PropsWithChildren<Record<string, unknown>>) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }: React.PropsWithChildren) => <>{children}</>
}));

vi.mock('date-fns', () => ({
  format: vi.fn((_date, _format) => 'January 1, 2023')
}));

const mockMemory = {
  id: '1',
  type: 'photo' as const,
  title: 'Beach Vacation',
  date: new Date('2023-01-01'),
  yearsAgo: 1,
  images: [MOCK_IMAGES.POSTS[0], MOCK_IMAGES.POSTS[1]],
  content: 'Great day at the beach with friends!',
  location: 'Miami Beach, FL',
  peopleTagged: [
    {
      id: '1',
      name: 'John Doe',
      avatar: MOCK_IMAGES.AVATARS[0]
    }
  ],
  interactions: {
    likes: 15,
    comments: 3,
    shares: 2
  },
  isLiked: true,
  comments: [
    {
      id: '1',
      author: {
        name: 'Jane Smith',
        avatar: MOCK_IMAGES.AVATARS[1]
      },
      content: 'Looks amazing!',
      timestamp: '2 hours ago'
    }
  ]
};

const defaultProps = {
  memory: mockMemory,
  isOpen: true,
  onClose: vi.fn()
};

describe('MemoryDetail', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders memory details when open', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    expect(screen.getByText('Beach Vacation')).toBeInTheDocument();
    expect(screen.getByText('Great day at the beach with friends!')).toBeInTheDocument();
    expect(screen.getByText('Miami Beach, FL')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(<MemoryDetail {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Beach Vacation')).not.toBeInTheDocument();
  });

  it('shows memory images', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const image = screen.getByAltText('Memory 1');
    expect(image).toBeInTheDocument();
    expect(image).toHaveAttribute('src', MOCK_IMAGES.POSTS[0]);
  });

  it('shows navigation arrows when multiple images exist', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const prevButton = screen.getByRole('button', { name: /previous/i });
    const nextButton = screen.getByRole('button', { name: /next/i });
    
    expect(prevButton).toBeInTheDocument();
    expect(nextButton).toBeInTheDocument();
  });

  it('navigates between images when arrows are clicked', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const nextButton = screen.getByRole('button', { name: /next/i });
    fireEvent.click(nextButton);
    
    const image = screen.getByAltText('Memory 2');
    expect(image).toHaveAttribute('src', MOCK_IMAGES.POSTS[1]);
  });

  it('shows memory metadata', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    expect(screen.getByText('January 1, 2023 • 1 year ago')).toBeInTheDocument();
    expect(screen.getByText('15 likes')).toBeInTheDocument();
    expect(screen.getByText('3 comments')).toBeInTheDocument();
  });

  it('shows tagged people', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    expect(screen.getByText('With:')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('displays existing comments', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Looks amazing!')).toBeInTheDocument();
    expect(screen.getByText('2 hours ago')).toBeInTheDocument();
  });

  it('allows adding new comments', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const commentInput = screen.getByPlaceholderText('Write a comment...');
    const postButton = screen.getByRole('button', { name: 'Post' });
    
    fireEvent.change(commentInput, { target: { value: 'Nice photo!' } });
    fireEvent.click(postButton);
    
    expect(toast.success).toHaveBeenCalledWith('Comment added!');
  });

  it('handles like button click', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const likeButton = screen.getByRole('button', { name: /like/i });
    fireEvent.click(likeButton);
    
    expect(toast.success).toHaveBeenCalledWith('Memory reaction updated!');
  });

  it('handles share button click', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const shareButton = screen.getByRole('button', { name: /share/i });
    fireEvent.click(shareButton);
    
    expect(toast.success).toHaveBeenCalledWith('Shared memory: Beach Vacation');
  });

  it('shows no images message when memory has no images', () => {
    const memoryWithoutImages = {
      ...mockMemory,
      images: []
    };
    
    render(<MemoryDetail {...defaultProps} memory={memoryWithoutImages} />);
    
    expect(screen.getByText('No images available for this memory')).toBeInTheDocument();
  });

  it('shows no comments message when memory has no comments', () => {
    const memoryWithoutComments = {
      ...mockMemory,
      comments: []
    };
    
    render(<MemoryDetail {...defaultProps} memory={memoryWithoutComments} />);
    
    expect(screen.getByText('No comments yet. Be the first to comment!')).toBeInTheDocument();
  });

  it('disables post button when comment input is empty', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const postButton = screen.getByRole('button', { name: 'Post' });
    expect(postButton).toBeDisabled();
  });

  it('enables post button when comment input has text', () => {
    render(<MemoryDetail {...defaultProps} />);
    
    const commentInput = screen.getByPlaceholderText('Write a comment...');
    const postButton = screen.getByRole('button', { name: 'Post' });
    
    fireEvent.change(commentInput, { target: { value: 'Test comment' } });
    
    expect(postButton).not.toBeDisabled();
  });
});
