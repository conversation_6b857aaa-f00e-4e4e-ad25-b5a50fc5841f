import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import MessageList from '@/components/messages/MessageList';
import { MOCK_IMAGES } from '@/lib/constants';

const mockConversations = [
  {
    id: '1',
    user: {
      name: '<PERSON>',
      avatar: MOCK_IMAGES.AVATARS[0],
      isOnline: true,
      lastActive: 'Active now'
    },
    lastMessage: {
      content: 'Hello there!',
      timestamp: '2h',
      isRead: false
    },
    unreadCount: 2
  },
  {
    id: '2',
    user: {
      name: '<PERSON>',
      avatar: MOCK_IMAGES.AVATARS[1],
      isOnline: false,
      lastActive: '3h ago'
    },
    lastMessage: {
      content: 'How are you?',
      timestamp: '1d',
      isRead: true
    },
    unreadCount: 0
  }
];

const defaultProps = {
  conversations: mockConversations,
  selectedConversationId: null,
  searchQuery: '',
  onSearchChange: vi.fn(),
  onSelectConversation: vi.fn(),
  isMobile: false,
  showConversation: false
};

describe('MessageList', () => {
  it('renders conversations list', () => {
    render(<MessageList {...defaultProps} />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('Hello there!')).toBeInTheDocument();
    expect(screen.getByText('How are you?')).toBeInTheDocument();
  });

  it('shows search input', () => {
    render(<MessageList {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages');
    expect(searchInput).toBeInTheDocument();
  });

  it('calls onSearchChange when search input changes', () => {
    const onSearchChange = vi.fn();
    render(<MessageList {...defaultProps} onSearchChange={onSearchChange} />);
    
    const searchInput = screen.getByPlaceholderText('Search messages');
    fireEvent.change(searchInput, { target: { value: 'John' } });
    
    expect(onSearchChange).toHaveBeenCalledWith('John');
  });

  it('calls onSelectConversation when conversation is clicked', () => {
    const onSelectConversation = vi.fn();
    render(<MessageList {...defaultProps} onSelectConversation={onSelectConversation} />);
    
    const conversation = screen.getByText('John Doe').closest('div');
    fireEvent.click(conversation!);
    
    expect(onSelectConversation).toHaveBeenCalledWith('1');
  });

  it('filters conversations based on search query', () => {
    render(<MessageList {...defaultProps} searchQuery="John" />);
    
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument();
  });

  it('shows unread count badge when conversation has unread messages', () => {
    render(<MessageList {...defaultProps} />);
    
    expect(screen.getByText('2')).toBeInTheDocument(); // Unread count badge
  });

  it('highlights selected conversation', () => {
    render(<MessageList {...defaultProps} selectedConversationId="1" />);
    
    const conversation = screen.getByText('John Doe').closest('div');
    expect(conversation).toHaveClass('bg-blue-50');
  });

  it('hides on mobile when showing conversation', () => {
    const { container } = render(
      <MessageList {...defaultProps} isMobile={true} showConversation={true} />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('shows on mobile when not showing conversation', () => {
    render(<MessageList {...defaultProps} isMobile={true} showConversation={false} />);
    
    expect(screen.getByText('Messages')).toBeInTheDocument();
  });
});
