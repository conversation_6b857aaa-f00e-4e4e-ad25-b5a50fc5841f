import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Settings,
  Palette,
  Shield,
  Download,
  MessageSquare,
  Phone,
  Video,
  Bell,
  Moon,
  Sun,
  Monitor,
  X,
  Save,
  RotateCcw
} from 'lucide-react';
import { ChatSettings } from '@/types/enhanced-messaging';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import { toast } from 'sonner';

interface MessagingSettingsProps {
  isOpen: boolean;
  onClose: () => void;
  currentUserId: string;
}

const MessagingSettings: React.FC<MessagingSettingsProps> = ({
  isOpen,
  onClose,
  currentUserId
}) => {
  const { settings, updateSettings } = useEnhancedMessaging(currentUserId);
  const [localSettings, setLocalSettings] = useState<ChatSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  const handleSettingChange = (key: keyof ChatSettings, value: unknown) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const saveSettings = () => {
    updateSettings(localSettings);
    setHasChanges(false);
    toast.success('Settings saved successfully');
  };

  const resetSettings = () => {
    setLocalSettings(settings);
    setHasChanges(false);
    toast.info('Settings reset');
  };

  const getThemeIcon = (theme: string) => {
    switch (theme) {
      case 'light': return <Sun className="w-4 h-4" />;
      case 'dark': return <Moon className="w-4 h-4" />;
      default: return <Monitor className="w-4 h-4" />;
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.95, opacity: 0 }}
          className="w-full max-w-4xl max-h-[90vh] bg-white dark:bg-gray-900 rounded-xl shadow-2xl overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <Settings className="w-6 h-6 text-blue-600" />
              <div>
                <h2 className="text-xl font-semibold">Messaging Settings</h2>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  Customize your messaging experience
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasChanges && (
                <Badge variant="secondary" className="text-orange-600">
                  Unsaved changes
                </Badge>
              )}
              <Button variant="ghost" onClick={onClose}>
                <X className="w-5 h-5" />
              </Button>
            </div>
          </div>

          <div className="flex h-[calc(90vh-120px)]">
            {/* Content */}
            <div className="flex-1 overflow-hidden">
              <Tabs defaultValue="appearance" className="h-full">
                <TabsList className="grid w-full grid-cols-5 p-1 m-4">
                  <TabsTrigger value="appearance" className="flex items-center gap-2">
                    <Palette className="w-4 h-4" />
                    Appearance
                  </TabsTrigger>
                  <TabsTrigger value="notifications" className="flex items-center gap-2">
                    <Bell className="w-4 h-4" />
                    Notifications
                  </TabsTrigger>
                  <TabsTrigger value="privacy" className="flex items-center gap-2">
                    <Shield className="w-4 h-4" />
                    Privacy
                  </TabsTrigger>
                  <TabsTrigger value="calls" className="flex items-center gap-2">
                    <Phone className="w-4 h-4" />
                    Calls
                  </TabsTrigger>
                  <TabsTrigger value="data" className="flex items-center gap-2">
                    <Download className="w-4 h-4" />
                    Data
                  </TabsTrigger>
                </TabsList>

                <ScrollArea className="h-[calc(100%-80px)] px-4 pb-4">
                  {/* Appearance Tab */}
                  <TabsContent value="appearance" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Palette className="w-5 h-5" />
                          Theme
                        </CardTitle>
                        <CardDescription>
                          Choose how messages appear to you
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label>Color Theme</Label>
                          <Select
                            value={localSettings.theme}
                            onValueChange={(value) => handleSettingChange('theme', value)}
                          >
                            <SelectTrigger>
                              <SelectValue>
                                <div className="flex items-center gap-2">
                                  {getThemeIcon(localSettings.theme)}
                                  {localSettings.theme === 'system' ? 'System' :
                                   localSettings.theme === 'light' ? 'Light' : 'Dark'}
                                </div>
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="system">
                                <div className="flex items-center gap-2">
                                  <Monitor className="w-4 h-4" />
                                  System
                                </div>
                              </SelectItem>
                              <SelectItem value="light">
                                <div className="flex items-center gap-2">
                                  <Sun className="w-4 h-4" />
                                  Light
                                </div>
                              </SelectItem>
                              <SelectItem value="dark">
                                <div className="flex items-center gap-2">
                                  <Moon className="w-4 h-4" />
                                  Dark
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Font Size</Label>
                          <Select
                            value={localSettings.fontSize}
                            onValueChange={(value) => handleSettingChange('fontSize', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="small">Small</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="large">Large</SelectItem>
                              <SelectItem value="extra-large">Extra Large</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <MessageSquare className="w-5 h-5" />
                          Message Behavior
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="space-y-0.5">
                            <Label>Enter to Send</Label>
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                              Press Enter to send messages instead of Ctrl+Enter
                            </p>
                          </div>
                          <Switch
                            checked={localSettings.enterToSend}
                            onCheckedChange={(checked) => handleSettingChange('enterToSend', checked)}
                          />
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Notifications Tab */}
                  <TabsContent value="notifications" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Bell className="w-5 h-5" />
                          Sound & Alerts
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {settingItems.notifications.map(setting => (
                          <SettingSwitch key={setting.key} setting={setting} />
                        ))}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Privacy Tab */}
                  <TabsContent value="privacy" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Shield className="w-5 h-5" />
                          Activity Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {settingItems.privacy.map(setting => (
                          <SettingSwitch key={setting.key} setting={setting} />
                        ))}
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Calls Tab */}
                  <TabsContent value="calls" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Video className="w-5 h-5" />
                          Video & Audio
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label>Default Call Type</Label>
                          <Select defaultValue="video">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="audio">
                                <div className="flex items-center gap-2">
                                  <Phone className="w-4 h-4" />
                                  Audio Only
                                </div>
                              </SelectItem>
                              <SelectItem value="video">
                                <div className="flex items-center gap-2">
                                  <Video className="w-4 h-4" />
                                  Video Call
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Call Quality</Label>
                          <Select defaultValue="auto">
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">Low (Save Data)</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High</SelectItem>
                              <SelectItem value="auto">Auto</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Data Tab */}
                  <TabsContent value="data" className="space-y-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Download className="w-5 h-5" />
                          Data Usage
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="space-y-2">
                          <Label>Auto-download Media</Label>
                          <Select
                            value={localSettings.autoDownloadMedia}
                            onValueChange={(value) => handleSettingChange('autoDownloadMedia', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="never">Never</SelectItem>
                              <SelectItem value="wifi">On Wi-Fi Only</SelectItem>
                              <SelectItem value="always">Always</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Data Usage</Label>
                          <Select
                            value={localSettings.dataUsage}
                            onValueChange={(value) => handleSettingChange('dataUsage', value)}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="low">Low (Compressed)</SelectItem>
                              <SelectItem value="medium">Medium</SelectItem>
                              <SelectItem value="high">High (Best Quality)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle>Storage</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span>Message Cache</span>
                          <Button variant="outline" size="sm">
                            Clear (2.4 MB)
                          </Button>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>Media Cache</span>
                          <Button variant="outline" size="sm">
                            Clear (45.2 MB)
                          </Button>
                        </div>
                        <div className="flex items-center justify-between">
                          <span>All Data</span>
                          <Button variant="destructive" size="sm">
                            Clear All
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </ScrollArea>
              </Tabs>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Changes are saved automatically
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                onClick={resetSettings}
                disabled={!hasChanges}
                className="flex items-center gap-2"
              >
                <RotateCcw className="w-4 h-4" />
                Reset
              </Button>
              <Button
                onClick={saveSettings}
                disabled={!hasChanges}
                className="flex items-center gap-2"
              >
                <Save className="w-4 h-4" />
                Save Changes
              </Button>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

export default MessagingSettings;
