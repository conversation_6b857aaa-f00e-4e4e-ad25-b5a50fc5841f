import { EventEmitter } from '@/utils/EventEmitter';

interface PerformanceMetrics {
  fps: number;
  memory: number;
  renderTime: number;
  networkLatency: number;
  bundleSize: number;
  cacheHitRate: number;
  componentMountTime: number;
  imageLoadTime: number;
}

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

interface OptimizationConfig {
  enableVirtualScrolling: boolean;
  enableImageLazyLoading: boolean;
  enableComponentCaching: boolean;
  enableBundleSplitting: boolean;
  cacheSize: number;
  cacheTTL: number;
  debounceDelay: number;
  throttleDelay: number;
}

class PerformanceService extends EventEmitter {
  private static instance: PerformanceService;
  private cache = new Map<string, CacheEntry<unknown>>();
  private metrics: PerformanceMetrics;
  private config: OptimizationConfig;
  private observedComponents = new Map<string, number>();
  private imageObserver: IntersectionObserver | null = null;
  private componentObserver: IntersectionObserver | null = null;
  private performanceTimers = new Map<string, number>();

  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService();
    }
    return PerformanceService.instance;
  }

  constructor() {
    super();
    
    this.metrics = {
      fps: 60,
      memory: 0,
      renderTime: 0,
      networkLatency: 0,
      bundleSize: 0,
      cacheHitRate: 0,
      componentMountTime: 0,
      imageLoadTime: 0
    };

    this.config = {
      enableVirtualScrolling: true,
      enableImageLazyLoading: true,
      enableComponentCaching: true,
      enableBundleSplitting: true,
      cacheSize: 1000,
      cacheTTL: 5 * 60 * 1000, // 5 minutes
      debounceDelay: 200,
      throttleDelay: 100
    };

    this.initializeObservers();
    this.startPerformanceMonitoring();
  }

  // Cache Management
  setCache<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.cacheTTL,
      accessCount: 0,
      lastAccessed: Date.now()
    };

    // Implement LRU eviction
    if (this.cache.size >= this.config.cacheSize) {
      this.evictLRU();
    }

    this.cache.set(key, entry);
    this.updateCacheHitRate();
  }

  getCache<T>(key: string): T | null {
    const entry = this.cache.get(key) as CacheEntry<T> | undefined;
    
    if (!entry) {
      this.updateCacheHitRate();
      return null;
    }

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.updateCacheHitRate();
      return null;
    }

    // Update access statistics
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    this.updateCacheHitRate();
    
    return entry.data;
  }

  clearCache(): void {
    this.cache.clear();
    this.updateCacheHitRate();
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private updateCacheHitRate(): void {
    // Calculate cache hit rate based on recent operations
    const totalOperations = this.cache.size;
    const hits = Array.from(this.cache.values()).filter(entry => entry.accessCount > 0).length;
    this.metrics.cacheHitRate = totalOperations > 0 ? (hits / totalOperations) * 100 : 0;
  }

  // Performance Monitoring
  private startPerformanceMonitoring(): void {
    // Monitor FPS
    this.monitorFPS();
    
    // Monitor memory usage
    this.monitorMemory();
    
    // Monitor network performance
    this.monitorNetwork();

    // Emit metrics every 5 seconds
    setInterval(() => {
      this.emit('metrics-updated', this.metrics);
    }, 5000);
  }

  private monitorFPS(): void {
    let frames = 0;
    let lastTime = performance.now();

    const countFrame = () => {
      frames++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        this.metrics.fps = Math.round((frames * 1000) / (currentTime - lastTime));
        frames = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(countFrame);
    };

    requestAnimationFrame(countFrame);
  }

  private monitorMemory(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as unknown as { memory: { usedJSHeapSize: number } }).memory;
        this.metrics.memory = Math.round(memory.usedJSHeapSize / 1024 / 1024); // MB
      }, 2000);
    }
  }

  private monitorNetwork(): void {
    if ('connection' in navigator) {
      const connection = (navigator as unknown as { connection: { rtt: number } }).connection;
      this.metrics.networkLatency = connection.rtt || 0;
    }
  }

  // Component Performance Tracking
  startComponentTimer(componentName: string): void {
    this.performanceTimers.set(componentName, performance.now());
  }

  endComponentTimer(componentName: string): number {
    const startTime = this.performanceTimers.get(componentName);
    if (!startTime) return 0;

    const duration = performance.now() - startTime;
    this.performanceTimers.delete(componentName);
    
    // Update metrics
    if (componentName.includes('mount')) {
      this.metrics.componentMountTime = duration;
    } else {
      this.metrics.renderTime = duration;
    }

    return duration;
  }

  // Image Lazy Loading
  private initializeObservers(): void {
    // Image lazy loading observer
    this.imageObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const startTime = performance.now();
            
            img.onload = () => {
              this.metrics.imageLoadTime = performance.now() - startTime;
            };

            if (img.dataset.src) {
              img.src = img.dataset.src;
              img.removeAttribute('data-src');
            }
            
            this.imageObserver?.unobserve(img);
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    // Component visibility observer
    this.componentObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const componentName = entry.target.getAttribute('data-component-name') || 'unknown';
          
          if (entry.isIntersecting) {
            this.observedComponents.set(componentName, Date.now());
            this.emit('component-visible', componentName);
          } else {
            const startTime = this.observedComponents.get(componentName);
            if (startTime) {
              const visibilityDuration = Date.now() - startTime;
              this.emit('component-hidden', { componentName, duration: visibilityDuration });
              this.observedComponents.delete(componentName);
            }
          }
        });
      },
      { threshold: 0.1 }
    );
  }

  observeImage(img: HTMLImageElement): void {
    if (this.config.enableImageLazyLoading && this.imageObserver) {
      this.imageObserver.observe(img);
    }
  }

  observeComponent(element: HTMLElement, componentName: string): void {
    element.setAttribute('data-component-name', componentName);
    this.componentObserver?.observe(element);
  }

  // Debounce and Throttle utilities
  debounce<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay?: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: NodeJS.Timeout;
    const actualDelay = delay || this.config.debounceDelay;
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func(...args), actualDelay);
    };
  }

  throttle<T extends (...args: unknown[]) => unknown>(
    func: T,
    delay?: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0;
    const actualDelay = delay || this.config.throttleDelay;
    
    return (...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCall >= actualDelay) {
        lastCall = now;
        func(...args);
      }
    };
  }

  // Bundle Analysis
  analyzeBundleSize(): void {
    if (typeof performance !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      this.metrics.bundleSize = navigation.transferSize || 0;
    }
  }

  // Configuration Management
  updateConfig(newConfig: Partial<OptimizationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config-updated', this.config);
  }

  getConfig(): OptimizationConfig {
    return { ...this.config };
  }

  // Metrics Access
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  // Resource Management
  preloadImage(src: string): Promise<void> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve();
      img.onerror = () => reject(new Error(`Failed to load image: ${src}`));
      img.src = src;
    });
  }

  preloadImages(sources: string[]): Promise<void[]> {
    return Promise.all(sources.map(src => this.preloadImage(src)));
  }

  // Memory Management
  cleanupMemory(): void {
    // Clear expired cache entries
    const now = Date.now();
    for (const [key, entry] of this.cache) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }

    // Force garbage collection if available
    if ('gc' in window) {
      (window as unknown as { gc: () => void }).gc();
    }

    this.emit('memory-cleaned');
  }

  // Service Worker Integration
  async registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
    if ('serviceWorker' in navigator) {
      try {
        const registration = await navigator.serviceWorker.register('/sw.js');
        console.log('Service Worker registered successfully');
        return registration;
      } catch (error) {
        console.error('Service Worker registration failed:', error);
        return null;
      }
    }
    return null;
  }

  // Cleanup
  destroy(): void {
    this.imageObserver?.disconnect();
    this.componentObserver?.disconnect();
    this.cache.clear();
    this.observedComponents.clear();
    this.performanceTimers.clear();
    this.removeAllListeners();
  }
}

export default PerformanceService;
export type { PerformanceMetrics, OptimizationConfig };
