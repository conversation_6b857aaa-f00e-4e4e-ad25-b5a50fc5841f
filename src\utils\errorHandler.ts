// Global error handling utilities for unhandled promise rejections and window errors

import { toast } from 'sonner';

interface ErrorReport {
  type: 'unhandledrejection' | 'error' | 'network' | 'chunk';
  message: string;
  stack?: string;
  url?: string;
  line?: number;
  column?: number;
  timestamp: string;
  userAgent: string;
  errorId: string;
}

class GlobalErrorHandler {
  private isInitialized = false;
  private errorQueue: ErrorReport[] = [];
  private readonly maxQueueSize = 50;

  // Initialize global error handlers
  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
    
    // Handle general window errors
    window.addEventListener('error', this.handleError);
    
    // Handle resource loading errors
    window.addEventListener('error', this.handleResourceError, true);

    this.isInitialized = true;
    console.log('Global error handler initialized');
  }

  // Clean up event listeners
  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
    window.removeEventListener('error', this.handleError);
    window.removeEventListener('error', this.handleResourceError, true);
    
    this.isInitialized = false;
  }

  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    console.error('Unhandled promise rejection:', event.reason);
    
    // Check if it's a chunk loading error (common in React apps)
    const isChunkError = event.reason?.toString().includes('Loading chunk') || 
                        event.reason?.toString().includes('ChunkLoadError');
    
    if (isChunkError) {
      this.handleChunkLoadError(event.reason);
      return;
    }

    // Check if it's a network error
    const isNetworkError = event.reason?.toString().includes('fetch') ||
                          event.reason?.toString().includes('NetworkError') ||
                          event.reason?.name === 'TypeError';

    if (isNetworkError) {
      this.handleNetworkError(event.reason);
      return;
    }

    const errorReport: ErrorReport = {
      type: 'unhandledrejection',
      message: event.reason?.message || event.reason?.toString() || 'Unknown promise rejection',
      stack: event.reason?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Show user-friendly toast
    toast.error('Something went wrong. Please try again.');
    
    // Prevent the default browser behavior
    event.preventDefault();
  };

  private handleError = (event: ErrorEvent): void => {
    console.error('Global error:', event.error);

    const errorReport: ErrorReport = {
      type: 'error',
      message: event.message || 'Unknown error',
      stack: event.error?.stack,
      url: event.filename,
      line: event.lineno,
      column: event.colno,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Don't show toast for every JS error to avoid spam
    // Only show for critical errors
    if (this.isCriticalError(event.error)) {
      toast.error('An unexpected error occurred.');
    }
  };

  private handleResourceError = (event: Event): void => {
    const target = event.target as HTMLElement;
    
    if (target && target !== window) {
      const tagName = target.tagName?.toLowerCase();
      const src = (target as HTMLImageElement | HTMLLinkElement | HTMLScriptElement).src || 
                   (target as HTMLLinkElement).href;
      
      console.warn(`Failed to load resource: ${tagName} - ${src}`);
      
      // Don't report every resource error, only critical ones
      if (this.isCriticalResource(tagName, src)) {
        const errorReport: ErrorReport = {
          type: 'network',
          message: `Failed to load ${tagName}: ${src}`,
          url: src,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          errorId: this.generateErrorId()
        };

        this.reportError(errorReport);
      }
    }
  };

  private handleChunkLoadError = (error: unknown): void => {
    console.warn('Chunk loading error detected, attempting to reload page...');
    
    const errorReport: ErrorReport = {
      type: 'chunk',
      message: 'Chunk loading error - app needs refresh',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Show specific message for chunk errors
    toast.error('App update detected. Refreshing page...', {
      duration: 3000,
    });
    
    // Refresh the page after a short delay
    setTimeout(() => {
      window.location.reload();
    }, 2000);
  };

  private handleNetworkError = (error: unknown): void => {
    console.warn('Network error detected:', error);
    
    const errorReport: ErrorReport = {
      type: 'network',
      message: 'Network error occurred',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      errorId: this.generateErrorId()
    };

    this.reportError(errorReport);
    
    // Check if user is offline
    if (!navigator.onLine) {
      toast.error('You appear to be offline. Please check your connection.');
    } else {
      toast.error('Network error. Please try again.');
    }
  };

  private isCriticalError = (error: Error): boolean => {
    if (!error) return false;
    
    const criticalPatterns = [
      'ChunkLoadError',
      'Script error',
      'ReferenceError',
      'TypeError: Cannot read prop'
    ];
    
    return criticalPatterns.some(pattern => 
      error.message?.includes(pattern) || error.name?.includes(pattern)
    );
  };

  private isCriticalResource = (tagName: string, src: string): boolean => {
    // Only report failures for critical resources
    const criticalTags = ['script', 'link'];
    const criticalPatterns = ['/static/', '/assets/', '.js', '.css'];
    
    return criticalTags.includes(tagName) && 
           criticalPatterns.some(pattern => src?.includes(pattern));
  };

  private reportError = (errorReport: ErrorReport): void => {
    // Add to queue
    this.errorQueue.push(errorReport);
    
    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift();
    }
    
    // In production, send to error reporting service
    if (import.meta.env.PROD) {
      this.sendToErrorService(errorReport);
    } else {
      console.group('🚨 Error Report');
      console.error(errorReport);
      console.groupEnd();
    }
  };

  private sendToErrorService = async (errorReport: ErrorReport): Promise<void> => {
    try {
      // Example implementation - replace with your error service
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });
      
      console.log('Would send to error service:', errorReport);
    } catch (sendError) {
      console.error('Failed to send error report:', sendError);
    }
  };

  private generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
  };

  // Get current error queue for debugging
  public getErrorQueue = (): ErrorReport[] => {
    return [...this.errorQueue];
  };

  // Clear error queue
  public clearErrorQueue = (): void => {
    this.errorQueue = [];
  };
}

// Create singleton instance
const globalErrorHandler = new GlobalErrorHandler();

// Initialize on module load
if (typeof window !== 'undefined') {
  globalErrorHandler.initialize();
}

export default globalErrorHandler;
export type { ErrorReport };
