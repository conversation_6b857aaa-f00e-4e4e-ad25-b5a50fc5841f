import { EventEmitter } from '../utils/EventEmitter';
import { 
  WebRTCSignal, 
  CallRequestSignal, 
  CallAcceptedSignal, 
  CallRejectedSignal, 
  CallEndedSignal 
} from '@/types/signal';

export interface CallParticipant {
  userId: string;
  stream?: MediaStream;
  isVideoEnabled: boolean;
  isAudioEnabled: boolean;
  isSpeaking: boolean;
  connection?: RTCPeerConnection;
}

export interface CallState {
  conversationId: string;
  type: 'audio' | 'video';
  status: 'initiating' | 'ringing' | 'connected' | 'ended';
  participants: CallParticipant[];
  localStream?: MediaStream;
  startTime?: Date;
  duration?: number;
}

export class WebRTCService extends EventEmitter {
  private peerConnections = new Map<string, RTCPeerConnection>();
  private localStream: MediaStream | null = null;
  private currentCall: CallState | null = null;
  private mediaConstraints = {
    video: {
      width: { min: 320, ideal: 1280, max: 1920 },
      height: { min: 240, ideal: 720, max: 1080 },
      frameRate: { ideal: 30, max: 60 }
    },
    audio: {
      echoCancellation: true,
      noiseSuppression: true,
      autoGainControl: true
    }
  };

  private iceServers = [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' },
    { urls: 'stun:stun2.l.google.com:19302' }
    // Add TURN servers for production
    // { 
    //   urls: 'turn:your-turn-server.com:3478',
    //   username: 'username',
    //   credential: 'password'
    // }
  ];

  constructor() {
    super();
    this.setupMediaDeviceHandlers();
  }

  private setupMediaDeviceHandlers() {
    // Monitor device changes
    navigator.mediaDevices.addEventListener('devicechange', () => {
      this.emit('deviceChanged');
    });
  }

  // Initialize call
  async initiateCall(conversationId: string, type: 'audio' | 'video'): Promise<void> {
    try {
      console.log(`Initiating ${type} call for conversation:`, conversationId);

      // Get user media
      const stream = await this.getUserMedia(type === 'video');
      this.localStream = stream;

      // Create call state
      this.currentCall = {
        conversationId,
        type,
        status: 'initiating',
        participants: [],
        localStream: stream,
        startTime: new Date()
      };

      this.emit('callInitiated', this.currentCall);
    } catch (error) {
      console.error('Failed to initiate call:', error);
      this.emit('callError', { error: 'Failed to access media devices' });
      throw error;
    }
  }

  // Accept incoming call
  async acceptCall(conversationId: string): Promise<void> {
    try {
      console.log('Accepting call for conversation:', conversationId);

      if (!this.currentCall || this.currentCall.conversationId !== conversationId) {
        throw new Error('No matching call to accept');
      }

      // Get user media if not already available
      if (!this.localStream) {
        const stream = await this.getUserMedia(this.currentCall.type === 'video');
        this.localStream = stream;
        this.currentCall.localStream = stream;
      }

      this.currentCall.status = 'connected';
      this.currentCall.startTime = new Date();

      this.emit('callAccepted', this.currentCall);
    } catch (error) {
      console.error('Failed to accept call:', error);
      this.emit('callError', { error: 'Failed to accept call' });
      throw error;
    }
  }

  // Reject call
  rejectCall(conversationId: string): void {
    console.log('Rejecting call for conversation:', conversationId);

    if (this.currentCall && this.currentCall.conversationId === conversationId) {
      this.currentCall.status = 'ended';
      this.emit('callRejected', this.currentCall);
      this.cleanup();
    }
  }

  // End call
  endCall(conversationId: string): void {
    console.log('Ending call for conversation:', conversationId);

    if (this.currentCall && this.currentCall.conversationId === conversationId) {
      this.currentCall.status = 'ended';
      this.currentCall.duration = this.currentCall.startTime 
        ? Date.now() - this.currentCall.startTime.getTime()
        : 0;

      this.emit('callEnded', this.currentCall);
      this.cleanup();
    }
  }

  // Create peer connection
  async createPeerConnection(userId: string): Promise<RTCPeerConnection> {
    const pc = new RTCPeerConnection({
      iceServers: this.iceServers
    });

    // Handle ICE candidates
    pc.onicecandidate = (event) => {
      if (event.candidate) {
        this.emit('iceCandidate', {
          userId,
          candidate: event.candidate
        });
      }
    };

    // Handle remote stream
    pc.ontrack = (event) => {
      console.log('Received remote stream from:', userId);
      const remoteStream = event.streams[0];
      
      if (this.currentCall) {
        const participant = this.currentCall.participants.find(p => p.userId === userId);
        if (participant) {
          participant.stream = remoteStream;
        } else {
          this.currentCall.participants.push({
            userId,
            stream: remoteStream,
            isVideoEnabled: true,
            isAudioEnabled: true,
            isSpeaking: false,
            connection: pc
          });
        }
      }

      this.emit('remoteStream', { userId, stream: remoteStream });
    };

    // Handle connection state changes
    pc.onconnectionstatechange = () => {
      console.log(`Connection state for ${userId}:`, pc.connectionState);
      this.emit('connectionStateChange', {
        userId,
        state: pc.connectionState
      });

      if (pc.connectionState === 'connected') {
        this.emit('participantConnected', userId);
      } else if (pc.connectionState === 'disconnected' || pc.connectionState === 'failed') {
        this.emit('participantDisconnected', userId);
      }
    };

    // Add local stream tracks
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        pc.addTrack(track, this.localStream!);
      });
    }

    this.peerConnections.set(userId, pc);
    return pc;
  }

  // Handle signaling
  async handleSignal(signal: WebRTCSignal): Promise<void> {
    const { userId, type, data } = signal;

    try {
      switch (type) {
        case 'offer':
          await this.handleOffer(userId, data.offer);
          break;
        case 'answer':
          await this.handleAnswer(userId, data.answer);
          break;
        case 'ice-candidate':
          await this.handleIceCandidate(userId, data.candidate);
          break;
        case 'call-request':
          this.handleCallRequest(signal);
          break;
        case 'call-accepted':
          this.handleCallAccepted(signal);
          break;
        case 'call-rejected':
          this.handleCallRejected(signal);
          break;
        case 'call-ended':
          this.handleCallEnded(signal);
          break;
        default:
          console.warn('Unknown signal type:', type);
      }
    } catch (error) {
      console.error('Error handling signal:', error);
    }
  }

  private async handleOffer(userId: string, offer: RTCSessionDescriptionInit): Promise<void> {
    let pc = this.peerConnections.get(userId);
    
    if (!pc) {
      pc = await this.createPeerConnection(userId);
    }

    await pc.setRemoteDescription(offer);
    const answer = await pc.createAnswer();
    await pc.setLocalDescription(answer);

    this.emit('sendSignal', {
      userId,
      type: 'answer',
      data: answer
    });
  }

  private async handleAnswer(userId: string, answer: RTCSessionDescriptionInit): Promise<void> {
    const pc = this.peerConnections.get(userId);
    if (pc) {
      await pc.setRemoteDescription(answer);
    }
  }

  private async handleIceCandidate(userId: string, candidate: RTCIceCandidateInit): Promise<void> {
    const pc = this.peerConnections.get(userId);
    if (pc) {
      await pc.addIceCandidate(candidate);
    }
  }

  private handleCallRequest(signal: CallRequestSignal): void {
    const { conversationId, callType, from } = signal;
    
    this.currentCall = {
      conversationId,
      type: callType,
      status: 'ringing',
      participants: [{
        userId: from,
        isVideoEnabled: callType === 'video',
        isAudioEnabled: true,
        isSpeaking: false
      }]
    };

    this.emit('callIncoming', {
      conversationId,
      type: callType,
      from
    });
  }

  private handleCallAccepted(_signal: CallAcceptedSignal): void {
    if (this.currentCall) {
      this.currentCall.status = 'connected';
      this.emit('callAccepted', this.currentCall);
    }
  }

  private handleCallRejected(_signal: CallRejectedSignal): void {
    if (this.currentCall) {
      this.currentCall.status = 'ended';
      this.emit('callRejected', this.currentCall);
      this.cleanup();
    }
  }

  private handleCallEnded(_signal: CallEndedSignal): void {
    if (this.currentCall) {
      this.currentCall.status = 'ended';
      this.emit('callEnded', this.currentCall);
      this.cleanup();
    }
  }

  // Media controls
  async toggleVideo(): Promise<boolean> {
    if (this.localStream) {
      const videoTracks = this.localStream.getVideoTracks();
      if (videoTracks.length > 0) {
        videoTracks[0].enabled = !videoTracks[0].enabled;
        this.emit('videoToggled', videoTracks[0].enabled);
        return videoTracks[0].enabled;
      }
    }
    return false;
  }

  async toggleAudio(): Promise<boolean> {
    if (this.localStream) {
      const audioTracks = this.localStream.getAudioTracks();
      if (audioTracks.length > 0) {
        audioTracks[0].enabled = !audioTracks[0].enabled;
        this.emit('audioToggled', audioTracks[0].enabled);
        return audioTracks[0].enabled;
      }
    }
    return false;
  }

  async switchCamera(): Promise<void> {
    if (this.localStream) {
      const videoTracks = this.localStream.getVideoTracks();
      if (videoTracks.length > 0) {
        const track = videoTracks[0];
        const constraints = track.getConstraints();
        const currentFacingMode = constraints.facingMode;
        
        const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user';
        
        try {
          await track.applyConstraints({
            facingMode: newFacingMode
          });
          this.emit('cameraSwitched', newFacingMode);
        } catch (error) {
          console.error('Failed to switch camera:', error);
        }
      }
    }
  }

  async shareScreen(): Promise<MediaStream | null> {
    try {
      const screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: true
      });

      // Replace video track in peer connections
      const videoTrack = screenStream.getVideoTracks()[0];
      if (videoTrack) {
        this.peerConnections.forEach(async (pc) => {
          const sender = pc.getSenders().find(s => 
            s.track && s.track.kind === 'video'
          );
          if (sender) {
            await sender.replaceTrack(videoTrack);
          }
        });

        // Handle screen share ending
        videoTrack.onended = () => {
          this.stopScreenShare();
        };
      }

      this.emit('screenShareStarted', screenStream);
      return screenStream;
    } catch (error) {
      console.error('Failed to start screen share:', error);
      return null;
    }
  }

  async stopScreenShare(): Promise<void> {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0];
      if (videoTrack) {
        // Replace screen share with camera
        this.peerConnections.forEach(async (pc) => {
          const sender = pc.getSenders().find(s => 
            s.track && s.track.kind === 'video'
          );
          if (sender) {
            await sender.replaceTrack(videoTrack);
          }
        });
      }
    }

    this.emit('screenShareStopped');
  }

  // Get user media
  private async getUserMedia(includeVideo: boolean): Promise<MediaStream> {
    const constraints: MediaStreamConstraints = {
      audio: this.mediaConstraints.audio,
      video: includeVideo ? this.mediaConstraints.video : false
    };

    try {
      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      console.error('Failed to get user media:', error);
      throw new Error('Unable to access camera/microphone');
    }
  }

  // Get available devices
  async getMediaDevices(): Promise<{
    cameras: MediaDeviceInfo[];
    microphones: MediaDeviceInfo[];
    speakers: MediaDeviceInfo[];
  }> {
    const devices = await navigator.mediaDevices.enumerateDevices();
    
    return {
      cameras: devices.filter(device => device.kind === 'videoinput'),
      microphones: devices.filter(device => device.kind === 'audioinput'),
      speakers: devices.filter(device => device.kind === 'audiooutput')
    };
  }

  // Cleanup
  private cleanup(): void {
    console.log('Cleaning up WebRTC resources');

    // Close peer connections
    this.peerConnections.forEach(pc => {
      pc.close();
    });
    this.peerConnections.clear();

    // Stop local stream
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => {
        track.stop();
      });
      this.localStream = null;
    }

    this.currentCall = null;
  }

  // Getters
  getCurrentCall(): CallState | null {
    return this.currentCall;
  }

  getLocalStream(): MediaStream | null {
    return this.localStream;
  }

  isInCall(): boolean {
    return this.currentCall !== null && this.currentCall.status !== 'ended';
  }

  // Destroy
  destroy(): void {
    this.cleanup();
    this.removeAllListeners();
  }
}
