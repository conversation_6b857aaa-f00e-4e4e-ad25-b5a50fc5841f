import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useInView } from 'react-intersection-observer';
import PostCard from '@/components/posts/PostCard';
import NewsFeedSkeleton from '@/components/NewsFeedSkeleton';
import { PerformanceMonitor, usePerformanceMonitor } from '@/utils/performanceOptimizer';
import { Button } from '@/components/ui/button';
import { RefreshCw, ArrowUp } from 'lucide-react';

interface Post {
  id: string;
  user_id: string;
  content: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  reactions?: Record<string, number>;
  feeling?: string;
  location?: string;
  tagged_friends?: string[];
  privacy?: string;
  is_live?: boolean;
  isPoll?: boolean;
  pollOptions?: string[];
  pollVotes?: Record<string, number>;
}

interface VirtualizedNewsFeedProps {
  posts: Post[];
  isLoading: boolean;
  isLoadingMore: boolean;
  hasNextPage: boolean;
  onLoadMore: () => void;
  onRefresh: () => void;
  onPostInteraction: (postId: string, action: string) => void;
}

const ITEM_HEIGHT = 400; // Estimated height per post
const OVERSCAN = 3; // Number of items to render outside viewport
const SCROLL_THRESHOLD = 1000; // Distance from bottom to trigger load more

const VirtualizedNewsFeed: React.FC<VirtualizedNewsFeedProps> = ({
  posts,
  isLoading,
  isLoadingMore,
  hasNextPage,
  onLoadMore,
  onRefresh,
  onPostInteraction
}) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(800);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [visiblePosts, setVisiblePosts] = useState<Set<string>>(new Set());
  
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);
  const lastScrollTop = useRef(0);
  const isScrollingDown = useRef(true);
  
  // Performance monitoring
  const { renderCount } = usePerformanceMonitor('VirtualizedNewsFeed');
  
  // Intersection observer for load more
  const { ref: loadMoreRef, inView: loadMoreInView } = useInView({
    threshold: 0,
    rootMargin: `${SCROLL_THRESHOLD}px`
  });

  // Calculate visible range
  const visibleRange = useMemo(() => {
    PerformanceMonitor.startMeasure('calculate-visible-range');
    
    const start = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - OVERSCAN);
    const visibleCount = Math.ceil(containerHeight / ITEM_HEIGHT);
    const end = Math.min(posts.length, start + visibleCount + OVERSCAN * 2);
    
    PerformanceMonitor.endMeasure('calculate-visible-range');
    
    return { start, end };
  }, [scrollTop, containerHeight, posts.length]);

  // Get visible posts with positioning
  const visiblePostsData = useMemo(() => {
    PerformanceMonitor.startMeasure('calculate-visible-posts');
    
    const result = posts.slice(visibleRange.start, visibleRange.end).map((post, index) => ({
      post,
      index: visibleRange.start + index,
      style: {
        position: 'absolute' as const,
        top: (visibleRange.start + index) * ITEM_HEIGHT,
        width: '100%',
        minHeight: ITEM_HEIGHT
      }
    }));
    
    PerformanceMonitor.endMeasure('calculate-visible-posts');
    
    return result;
  }, [posts, visibleRange]);

  // Total height for scrollbar
  const totalHeight = posts.length * ITEM_HEIGHT;

  // Optimized scroll handler
  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const target = event.currentTarget;
    const newScrollTop = target.scrollTop;
    
    // Determine scroll direction
    isScrollingDown.current = newScrollTop > lastScrollTop.current;
    lastScrollTop.current = newScrollTop;
    
    setScrollTop(newScrollTop);
    setShowScrollToTop(newScrollTop > 1000);
    
    // Update visible posts for analytics
    const currentVisible = new Set<string>();
    visiblePostsData.forEach(({ post }) => {
      currentVisible.add(post.id);
    });
    setVisiblePosts(currentVisible);
  }, [visiblePostsData]);

  // Handle container resize
  useEffect(() => {
    const updateContainerHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateContainerHeight();
    window.addEventListener('resize', updateContainerHeight);
    
    return () => {
      window.removeEventListener('resize', updateContainerHeight);
    };
  }, []);

  // Load more when in view
  useEffect(() => {
    if (loadMoreInView && hasNextPage && !isLoadingMore) {
      onLoadMore();
    }
  }, [loadMoreInView, hasNextPage, isLoadingMore, onLoadMore]);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    if (scrollElementRef.current) {
      scrollElementRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  }, []);

  // Memoized post interaction handler with visibility tracking
  const handlePostInteraction = useCallback((postId: string, action: string) => {
    PerformanceMonitor.startMeasure('post-interaction');

    // Update visible posts set for performance tracking
    if (action === 'view') {
      setVisiblePosts(prev => {
        if (prev.has(postId)) return prev; // Avoid unnecessary state update
        return new Set([...prev, postId]);
      });
    }

    onPostInteraction(postId, action);
    PerformanceMonitor.endMeasure('post-interaction');
  }, [onPostInteraction]);

  // Preload images for better performance
  const preloadImages = useCallback((posts: Post[]) => {
    posts.forEach(post => {
      if (post.image_url) {
        const img = new Image();
        img.src = post.image_url;
      }
      if (post.profiles?.avatar_url) {
        const img = new Image();
        img.src = post.profiles.avatar_url;
      }
    });
  }, []);

  // Preload images for visible posts
  useEffect(() => {
    const postsToPreload = posts.slice(
      Math.max(0, visibleRange.start - 2),
      Math.min(posts.length, visibleRange.end + 2)
    );
    preloadImages(postsToPreload);
  }, [posts, visibleRange, preloadImages]);

  if (isLoading && posts.length === 0) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 5 }).map((_, index) => (
          <NewsFeedSkeleton key={index} />
        ))}
      </div>
    );
  }

  return (
    <div className="relative">
      {/* Performance Debug Info (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-20 right-4 bg-black/80 text-white p-2 rounded text-xs z-50">
          <div>Renders: {renderCount}</div>
          <div>Visible: {visibleRange.start}-{visibleRange.end}</div>
          <div>Total: {posts.length}</div>
          <div>Height: {containerHeight}px</div>
        </div>
      )}

      {/* Refresh Button */}
      <div className="flex justify-center mb-4">
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={isLoading}
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Refresh Feed</span>
        </Button>
      </div>

      {/* Virtual Scroll Container */}
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{ height: '80vh' }}
      >
        <div
          ref={scrollElementRef}
          className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
          onScroll={handleScroll}
        >
          {/* Virtual content container */}
          <div
            className="relative"
            style={{ height: totalHeight }}
          >
            {/* Rendered posts */}
            {visiblePostsData.map(({ post, index, style }) => (
              <div
                key={post.id}
                style={style}
                className="px-4"
              >
                <PostCard
                  post={post}
                  onInteraction={handlePostInteraction}
                  isVisible={visiblePosts.has(post.id)}
                  priority={index < 3} // High priority for first 3 posts
                />
              </div>
            ))}
          </div>

          {/* Load more trigger */}
          {hasNextPage && (
            <div
              ref={loadMoreRef}
              className="flex justify-center py-8"
            >
              {isLoadingMore ? (
                <div className="space-y-4 w-full max-w-2xl">
                  {Array.from({ length: 2 }).map((_, index) => (
                    <NewsFeedSkeleton key={`loading-${index}`} />
                  ))}
                </div>
              ) : (
                <Button
                  variant="outline"
                  onClick={onLoadMore}
                  className="flex items-center space-x-2"
                >
                  <span>Load More Posts</span>
                </Button>
              )}
            </div>
          )}

          {/* End of feed message */}
          {!hasNextPage && posts.length > 0 && (
            <div className="text-center py-8 text-gray-500">
              <p>You've reached the end of your feed</p>
              <Button
                variant="ghost"
                onClick={onRefresh}
                className="mt-2"
              >
                Refresh to see new posts
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Scroll to top button */}
      {showScrollToTop && (
        <Button
          className="fixed bottom-20 right-4 rounded-full w-12 h-12 shadow-lg z-40"
          onClick={scrollToTop}
          size="sm"
        >
          <ArrowUp className="w-4 h-4" />
        </Button>
      )}

      {/* Empty state */}
      {posts.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <RefreshCw className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No posts to show</h3>
          <p className="text-gray-500 mb-4">
            Follow some friends or pages to see posts in your feed
          </p>
          <Button onClick={onRefresh}>
            Refresh Feed
          </Button>
        </div>
      )}
    </div>
  );
};

export default React.memo(VirtualizedNewsFeed);
