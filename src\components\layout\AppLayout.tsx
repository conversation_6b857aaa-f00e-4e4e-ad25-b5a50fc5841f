import React, { useState, useEffect, useMemo } from 'react';
import { useAuth } from '../../contexts/AuthContextType';
import { useIsMobile } from '@/hooks/use-device';
import { useLocation } from 'react-router-dom';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import RightSidebar from '@/components/RightSidebar';
import MobileNavigation from '@/components/MobileNavigation';
import ErrorBoundary from '@/components/ui/ErrorBoundary';
import { ROUTES } from '@/lib/constants';


interface AppLayoutProps {
  children: React.ReactNode;
  showSidebars?: boolean;
}

const AppLayout: React.FC<AppLayoutProps> = ({ 
  children, 
  showSidebars = true 
}) => {
  const { user } = useAuth();
  const isMobile = useIsMobile();
  const location = useLocation();
  const [showRightSidebar, setShowRightSidebar] = useState(false);
  
  // Optimize layout calculations with memoization
  const shouldShowRightSidebar = useMemo(() => {
    const isHomePage = location.pathname === ROUTES.HOME;
    return isHomePage && !isMobile && window.innerWidth >= 1024;
  }, [location.pathname, isMobile]);

  useEffect(() => {
    setShowRightSidebar(shouldShowRightSidebar);
  }, [shouldShowRightSidebar]);

  if (!user) {
    return null;
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-100 flex flex-col dark:bg-gray-900">
        <Header />
        
        <div className="flex flex-1 overflow-hidden">
          {/* Left Sidebar - Hidden on mobile */}
          {showSidebars && !isMobile && (
            <aside className="w-64 flex-shrink-0 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto scrollbar-thin">
              <React.Suspense fallback={<div className="p-4 animate-pulse">
                <div className="space-y-3">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="h-10 bg-gray-200 dark:bg-gray-700 rounded" />
                  ))}
                </div>
              </div>}>
                <Sidebar />
              </React.Suspense>
            </aside>
          )}
          
          {/* Main Content */}
          <main className="flex-1 overflow-y-auto px-2 sm:px-4 py-2 sm:py-4 pb-20 md:pb-8">
            {children}
          </main>
          
          {/* Right Sidebar - Only on Home page and desktop */}
          {showSidebars && showRightSidebar && (
            <aside className="w-80 flex-shrink-0 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto scrollbar-thin">
              <React.Suspense fallback={<div className="p-4 animate-pulse">
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded" />
                  ))}
                </div>
              </div>}>
                <RightSidebar />
              </React.Suspense>
            </aside>
          )}
        </div>
        
        {/* Mobile Navigation */}
        {isMobile && <MobileNavigation />}
      </div>
    </ErrorBoundary>
  );
};

export default AppLayout;