import React, { memo } from 'react';
import { PostCreator } from '@/components/shared';
import { useAuth } from '@/contexts/AuthContextType';
import { getSafeImage } from '@/lib/constants';

interface CreatePostProps {
  onCreatePost?: (post: {
    content: string;
    image_url?: string;
    feeling?: string;
    location?: string;
    tagged_friends?: string[];
    privacy?: string;
    is_live?: boolean;
    isPoll?: boolean;
    pollOptions?: string[];
    pollVotes?: Record<string, number>;
  }) => void;
}

const CreatePost = memo<CreatePostProps>(({ onCreatePost }) => {
  const { user } = useAuth();

  const handleCreatePost = (postData: {
    content: string;
    attachments?: File[];
    feeling?: string;
    location?: string;
    privacy?: string;
    taggedFriends?: string[];
    isPoll?: boolean;
    pollOptions?: string[];
  }) => {
    // Convert the shared component format to the expected format
    const post = {
      content: postData.content,
      image_url: postData.attachments?.[0] ? URL.createObjectURL(postData.attachments[0]) : undefined,
      feeling: postData.feeling || '',
      location: postData.location || '',
      tagged_friends: postData.taggedFriends || [],
      privacy: postData.privacy || 'public',
      is_live: false,
      isPoll: postData.isPoll || false,
      pollOptions: postData.pollOptions || [],
      pollVotes: postData.isPoll ? postData.pollOptions?.reduce((acc, option) => ({ ...acc, [option]: 0 }), {}) : undefined
    };
    
    onCreatePost?.(post);
  };

  if (!user) {
    return null;
  }

  return (
    <PostCreator
      user={{
        id: user.id,
        name: user.full_name || 'User',
        avatar: getSafeImage('AVATARS', 0)
      }}
      onCreatePost={handleCreatePost}
      placeholder="What's on your mind?"
      allowMedia={true}
      allowLocation={true}
      allowTagging={true}
      allowFeeling={true}
      className="mb-6"
    />
  );
});

CreatePost.displayName = 'CreatePost';

export default CreatePost;
