import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { motion } from 'framer-motion';
import {
  Search,
  Phone,
  Video,
  Info,
  Smile,
  Paperclip,
  Send,
  Settings,
  Users,
  Check,
  CheckCheck,
  Reply,
  X,
  ArrowLeft
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { formatDistanceToNow } from 'date-fns';
import { useEnhancedMessaging } from '@/hooks/useEnhancedMessaging';
import { Message } from '@/types/enhanced-messaging';

interface OptimizedMessagingProps {
  currentUserId: string;
  onClose?: () => void;
}

const OptimizedMessaging: React.FC<OptimizedMessagingProps> = ({
  currentUserId,
  onClose: _onClose
}) => {
  const {
    conversations,
    activeConversation,
    messages,
    isConnected,
    typingUsers,
    currentCall,
    selectConversation,
    sendMessage,
    startCall,
    endCall,
    setTyping,
  } = useEnhancedMessaging(currentUserId);

  // Mobile responsive state with improved detection
  const [isMobile, setIsMobile] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);
  
  // Message state
  const [newMessage, setNewMessage] = useState('');
  const [replyingTo, setReplyingTo] = useState<Message | null>(null);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Enhanced mobile detection and keyboard handling
  useEffect(() => {
    const checkMobile = () => {
      const mobile = window.innerWidth < 768;
      setIsMobile(mobile);
      
      // On desktop, always show sidebar
      // On mobile, hide sidebar when there's an active conversation
      if (!mobile) {
        setShowSidebar(true);
      } else {
        setShowSidebar(!activeConversation);
      }
    };
    
    // Enhanced keyboard detection for mobile
    const handleResize = () => {
      if (isMobile) {
        const heightDiff = window.innerHeight - document.documentElement.clientHeight;
        setIsKeyboardOpen(heightDiff > 150); // Keyboard is likely open if height difference > 150px
      }
      checkMobile();
    };
    
    checkMobile();
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', checkMobile);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', checkMobile);
    };
  }, [activeConversation, isMobile]);

  // Mobile behavior: hide sidebar when selecting conversation
  useEffect(() => {
    if (isMobile && activeConversation) {
      setShowSidebar(false);
    }
  }, [isMobile, activeConversation]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Online users
  const onlineUsers = useMemo(() => 
    conversations
      .filter(c => c.user?.isOnline)
      .slice(0, 8)
  , [conversations]);

  // Handle conversation select with improved mobile UX
  const handleSelectConversation = useCallback((conversationId: string) => {
    selectConversation(conversationId);
    // On mobile, hide sidebar after selecting
    if (isMobile) {
      setShowSidebar(false);
    }
  }, [selectConversation, isMobile, setShowSidebar]);

  // Handle back to conversations (mobile)
  const handleBackToConversations = useCallback(() => {
    if (isMobile) {
      setShowSidebar(true);
    }
  }, [isMobile]);

  // Send message handler with improved error handling
  const handleSendMessage = useCallback(async () => {
    if (!newMessage.trim() || !activeConversation) return;

    try {
      await sendMessage(newMessage, {
        replyTo: replyingTo?.id
      });
      
      setNewMessage('');
      setReplyingTo(null);
      setTyping(false);
      
      // Focus back to textarea
      textareaRef.current?.focus();
    } catch (error) {
      console.error('Failed to send message:', error);
      // TODO: Show error toast
    }
  }, [newMessage, activeConversation, replyingTo, sendMessage, setTyping]);

  // Handle Enter key for sending messages
  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  // Handle typing indicator
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setNewMessage(e.target.value);
    setTyping(e.target.value.length > 0);
  }, [setTyping]);

  // Message status
  const getMessageStatus = useCallback((message: Message) => {
    if (message.status === 'sent') {
      return <Check className="w-3 h-3 text-gray-400" />;
    } else if (message.status === 'delivered') {
      return <CheckCheck className="w-3 h-3 text-gray-400" />;
    } else if (message.status === 'read') {
      return <CheckCheck className="w-3 h-3 text-blue-500" />;
    }
    return null;
  }, []);

  // Format message time
  const formatMessageTime = useCallback((timestamp: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    }).format(new Date(timestamp));
  }, []);

  // Render message component
  const renderMessage = useCallback((message: Message) => {
    const isOwnMessage = message.senderId === currentUserId;
    
    // Find the original message being replied to
    const originalMessage = message.replyTo ? messages.find(m => m.id === message.replyTo) : null;
    
    return (
      <motion.div
        key={message.id}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-3`}
      >
        <div className={`max-w-[85%] ${isOwnMessage ? 'order-2' : 'order-1'} ${isMobile ? 'max-w-[85%]' : 'max-w-[70%]'}`}>
          {originalMessage && (
            <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-t-lg text-xs border-l-2 border-blue-500">
              <span className="text-gray-600 dark:text-gray-400">
                Replying to: {originalMessage.content?.slice(0, 50)}...
              </span>
            </div>
          )}
          
          <div className={`rounded-lg ${
            isOwnMessage 
              ? 'bg-blue-500 text-white' 
              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
          } ${isMobile ? 'p-4' : 'p-3'}`}>
            <p className={`break-words ${isMobile ? 'text-base leading-relaxed' : 'text-sm'}`}>{message.content || ''}</p>
            
            {message.reactions && message.reactions.length > 0 && (
              <div className="flex gap-1 mt-2">
                {message.reactions.map((reaction, index) => (
                  <span key={index} className="text-xs bg-white bg-opacity-20 rounded-full px-2 py-1">
                    {reaction.emoji}
                  </span>
                ))}
              </div>
            )}
          </div>
          
          <div className={`flex items-center gap-1 mt-1 text-xs text-gray-500 ${
            isOwnMessage ? 'justify-end' : 'justify-start'
          }`}>
            <span>{formatMessageTime(message.timestamp)}</span>
            {isOwnMessage && getMessageStatus(message)}
          </div>
        </div>
        
        {!isOwnMessage && (
          <Avatar className="w-6 h-6 order-1 mr-2">
            <AvatarImage src={activeConversation?.user?.avatar} />
            <AvatarFallback className="text-xs">
              {activeConversation?.user?.name?.[0]}
            </AvatarFallback>
          </Avatar>
        )}
      </motion.div>
    );
  }, [currentUserId, messages, activeConversation, formatMessageTime, getMessageStatus, isMobile]);

  // Handle call interface
  if (currentCall) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h3 className="text-lg font-semibold">Call in Progress</h3>
          <p>Video/Audio call functionality</p>
          <Button onClick={endCall} className="mt-4">
            End Call
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex h-full bg-white dark:bg-gray-900 relative ${isMobile ? 'flex-col md:flex-row' : ''}`}>
      {/* Mobile Overlay with improved touch handling */}
      {isMobile && showSidebar && activeConversation && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 touch-none"
          onClick={() => setShowSidebar(false)}
          onTouchStart={(e) => e.preventDefault()}
        />
      )}

      {/* Enhanced Sidebar with better mobile behavior */}
      <motion.div 
        className={`${
          isMobile 
            ? `fixed left-0 top-0 h-full w-full max-w-sm z-50 transform transition-transform duration-300 ease-in-out` 
            : 'w-80 relative'
        } ${
          isMobile && !showSidebar ? '-translate-x-full' : 'translate-x-0'
        } border-r border-gray-200 dark:border-gray-700 flex flex-col bg-white dark:bg-gray-900 ${
          isMobile ? 'shadow-2xl' : ''
        }`}
        initial={false}
        animate={{
          x: isMobile && !showSidebar ? '-100%' : '0%'
        }}
        transition={{ 
          type: "spring", 
          stiffness: 300, 
          damping: 30 
        }}
      >
        {/* Enhanced Header with better mobile spacing */}
        <div className={`p-4 border-b border-gray-200 dark:border-gray-700 ${isMobile ? 'px-6' : ''}`}>
          <div className="flex items-center justify-between mb-4">
            <h1 className={`font-semibold ${isMobile ? 'text-2xl' : 'text-xl'}`}>Messages</h1>
            <div className="flex gap-2">
              {isMobile && (
                <Button
                  variant="ghost"
                  size={isMobile ? "default" : "sm"}
                  onClick={() => setShowSidebar(false)}
                  className={isMobile ? "h-10 w-10 p-0" : ""}
                >
                  <X className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                </Button>
              )}
              <Button
                variant="ghost"
                size={isMobile ? "default" : "sm"}
                aria-label="Search messages"
                className={isMobile ? "h-10 w-10 p-0" : ""}
              >
                <Search className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
              </Button>
              <Button
                variant="ghost"
                size={isMobile ? "default" : "sm"}
                aria-label="Settings"
                className={isMobile ? "h-10 w-10 p-0" : ""}
              >
                <Settings className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
              </Button>
            </div>
          </div>

          {/* Connection Status */}
          <div className="flex items-center gap-2 text-sm bg-gray-50 dark:bg-gray-800 p-2 rounded-lg">
            <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
            <span className={`font-medium ${isConnected ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>

        {/* Online Users */}
        {onlineUsers.length > 0 && (
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Online Now ({onlineUsers.length})
            </h3>
            <div className="flex gap-2 overflow-x-auto pb-2">
              {onlineUsers.map((conversation) => (
                <button
                  key={conversation.id}
                  onClick={() => handleSelectConversation(conversation.id)}
                  className="flex-shrink-0 flex flex-col items-center gap-1 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors"
                >
                  <div className="relative">
                    <Avatar className="w-12 h-12">
                      <AvatarImage src={conversation.user?.avatar} />
                      <AvatarFallback>{conversation.user?.name?.[0]}</AvatarFallback>
                    </Avatar>
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-900" />
                  </div>
                  <span className="text-xs text-center truncate max-w-16">
                    {conversation.user?.name?.split(' ')[0]}
                  </span>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Conversations List */}
        <ScrollArea className="flex-1">
          <div className="p-2">
            {conversations.length === 0 ? (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <div className="text-sm">
                  {isConnected ? 'No conversations yet' : 'Loading conversations...'}
                </div>
                <div className="text-xs mt-1">
                  {isConnected ? 'Start a new conversation!' : 'Connecting to messaging service...'}
                </div>
              </div>
            ) : (
              conversations.map((conversation) => (
                <button
                  key={conversation.id}
                  onClick={() => handleSelectConversation(conversation.id)}
                  className={`w-full ${isMobile ? 'p-4' : 'p-3'} flex items-center gap-3 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-lg transition-colors mb-1 ${
                    activeConversation?.id === conversation.id
                      ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                      : ''
                  } ${isMobile ? 'active:bg-gray-100 dark:active:bg-gray-700' : ''}`}
                >
                  <Avatar className={isMobile ? "h-12 w-12" : "h-10 w-10"}>
                    <AvatarImage src={conversation.user?.avatar} />
                    <AvatarFallback className={isMobile ? "text-lg" : ""}>
                      {conversation.user?.name?.[0] || conversation.name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 text-left min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={`font-medium truncate ${isMobile ? 'text-base' : 'text-sm'}`}>
                        {conversation.user?.name || conversation.name}
                      </h3>
                      {conversation.lastMessage && (
                        <span className={`text-gray-500 flex-shrink-0 ml-2 ${isMobile ? 'text-sm' : 'text-xs'}`}>
                          {formatDistanceToNow(new Date(conversation.lastMessage.timestamp), { addSuffix: true })}
                        </span>
                      )}
                    </div>
                    
                    {conversation.lastMessage && (
                      <p className={`text-gray-500 truncate ${isMobile ? 'text-sm' : 'text-xs'}`}>
                        {conversation.lastMessage?.content || 'No messages yet'}
                      </p>
                    )}
                  </div>
                  
                  {conversation.unreadCount && conversation.unreadCount > 0 && (
                    <Badge variant="default" className={`ml-2 bg-blue-500 text-white ${isMobile ? 'text-sm px-2 py-1' : ''}`}>
                      {conversation.unreadCount}
                    </Badge>
                  )}
                </button>
              ))
            )}
          </div>
        </ScrollArea>
      </motion.div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <>
            {/* Enhanced Chat Header with better mobile layout */}
            <div className={`border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 ${isMobile ? 'p-4 pb-3' : 'p-4'}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 flex-1 min-w-0">
                  {isMobile && (
                    <Button
                      variant="ghost"
                      size={isMobile ? "default" : "sm"}
                      onClick={handleBackToConversations}
                      className={`flex-shrink-0 ${isMobile ? 'h-10 w-10 p-0 mr-1' : 'mr-2'}`}
                    >
                      <ArrowLeft className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                    </Button>
                  )}
                  
                  <Avatar className={isMobile ? "h-10 w-10 flex-shrink-0" : "h-8 w-8 flex-shrink-0"}>
                    <AvatarImage src={activeConversation.user?.avatar} />
                    <AvatarFallback className={isMobile ? "text-sm" : "text-xs"}>
                      {activeConversation.user?.name?.[0] || activeConversation.name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="min-w-0 flex-1">
                    <h2 className={`font-semibold truncate ${isMobile ? 'text-lg' : 'text-base'}`}>
                      {activeConversation.user?.name || activeConversation.name}
                    </h2>
                    <div className={`text-gray-500 ${isMobile ? 'text-sm' : 'text-xs'}`}>
                      {activeConversation.user?.isOnline ? (
                        <span className="text-green-500">● Online</span>
                      ) : (
                        <span>Last seen recently</span>
                      )}
                    </div>
                  </div>
                </div>

                <div className={`flex gap-1 flex-shrink-0 ${isMobile ? 'ml-2' : ''}`}>
                  <Button
                    variant="ghost"
                    size={isMobile ? "default" : "sm"}
                    onClick={() => startCall('audio')}
                    className={isMobile ? "h-10 w-10 p-0" : ""}
                  >
                    <Phone className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size={isMobile ? "default" : "sm"}
                    onClick={() => startCall('video')}
                    className={isMobile ? "h-10 w-10 p-0" : ""}
                  >
                    <Video className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size={isMobile ? "default" : "sm"}
                    className={isMobile ? "h-10 w-10 p-0" : ""}
                  >
                    <Info className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-2">
                {messages.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No messages yet. Start the conversation!</p>
                  </div>
                ) : (
                  messages.map(renderMessage)
                )}
                
                {/* Typing indicator */}
                {typingUsers.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <div className="flex gap-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100" />
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200" />
                    </div>
                    <span>{typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...</span>
                  </div>
                )}
                
                <div ref={messagesEndRef} />
              </div>
            </ScrollArea>

            {/* Reply indicator */}
            {replyingTo && (
              <div className="px-4 py-2 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Reply className="w-4 h-4 text-blue-500" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      Replying to: {replyingTo.content?.slice(0, 50)}...
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setReplyingTo(null)}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            )}

            {/* Enhanced Message Input with better mobile experience */}
            <div className={`border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 ${
              isMobile ? 'p-4 pb-6' : 'p-4'
            } ${isKeyboardOpen && isMobile ? 'pb-2' : ''}`}>
              <div className={`flex gap-2 items-end ${isMobile ? 'gap-3' : ''}`}>
                <div className={`flex gap-1 ${isMobile ? 'gap-2' : ''}`}>
                  <Button 
                    variant="ghost" 
                    size={isMobile ? "default" : "sm"}
                    className={isMobile ? "h-10 w-10 p-0" : ""}
                  >
                    <Paperclip className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size={isMobile ? "default" : "sm"}
                    className={isMobile ? "h-10 w-10 p-0" : ""}
                  >
                    <Smile className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                </div>
                
                <div className="flex-1 relative">
                  <Textarea
                    ref={textareaRef}
                    value={newMessage}
                    onChange={handleInputChange}
                    onKeyPress={handleKeyPress}
                    placeholder="Type a message..."
                    className={`resize-none ${
                      isMobile 
                        ? 'min-h-12 max-h-32 text-base px-4 py-3 pr-14 rounded-2xl' 
                        : 'min-h-10 max-h-32 pr-12'
                    }`}
                    rows={1}
                    style={{
                      fontSize: isMobile ? '16px' : '14px', // Prevents zoom on iOS
                    }}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className={`absolute right-2 top-1/2 transform -translate-y-1/2 ${
                      isMobile 
                        ? 'h-10 w-10 p-0 rounded-full' 
                        : 'h-8 w-8 p-0'
                    }`}
                  >
                    <Send className={`${isMobile ? 'w-5 h-5' : 'w-4 h-4'}`} />
                  </Button>
                </div>
              </div>
            </div>
          </>
        ) : (
          // Enhanced empty state with better mobile experience
          <div className={`flex-1 flex items-center justify-center ${isMobile ? 'p-8' : 'p-4'}`}>
            <div className="text-center text-gray-500 max-w-sm">
              <Users className={`mx-auto mb-4 text-gray-300 ${isMobile ? 'w-20 h-20' : 'w-16 h-16'}`} />
              <h3 className={`font-semibold mb-2 ${isMobile ? 'text-2xl' : 'text-xl'}`}>Your Messages</h3>
              <p className={`text-gray-400 max-w-md ${isMobile ? 'text-base mb-6' : 'text-sm'}`}>
                Select a conversation to start messaging
              </p>
              {isMobile && (
                <Button 
                  onClick={() => setShowSidebar(true)} 
                  className="mt-4 px-6 py-3 text-base"
                  size="lg"
                >
                  View Conversations
                </Button>
              )}
            </div>
          </div>
        )}
      </div>

    </div>
  );
};

export default OptimizedMessaging;
