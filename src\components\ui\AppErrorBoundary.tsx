import React, { Component, ReactNode, ErrorInfo } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react';
import { Button } from './button';
import { toast } from 'sonner';

interface AppErrorBoundaryProps {
  children: ReactNode;
}

interface AppErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
  errorId?: string;
}

interface ErrorReport {
  type: 'react-error';
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  errorId: string;
  retryCount: number;
}

class AppErrorBoundary extends Component<AppErrorBoundaryProps, AppErrorBoundaryState> {
  private retryCount = 0;
  private maxRetries = 2;

  constructor(props: AppErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): AppErrorBoundaryState {
    return {
      hasError: true,
      error,
      errorId: `app_error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.group('🚨 AppErrorBoundary: Critical React Error');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();
    
    this.setState({
      errorInfo
    });
    
    // Create detailed error report
    const errorReport: ErrorReport = {
      type: 'react-error',
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId || 'unknown',
      retryCount: this.retryCount
    };
    
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.group('📋 App Error Report');
      console.table(errorReport);
      console.groupEnd();
    }
    
    // Log to monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.logErrorToMonitoringService(errorReport);
    }
    
    // Show user notification
    toast.error('A critical error occurred in the application. Please try refreshing.', {
      duration: 5000,
      action: {
        label: 'Refresh',
        onClick: () => window.location.reload()
      }
    });
  }

  private logErrorToMonitoringService = async (_errorReport: ErrorReport) => {
    try {
      // In a real application, you would send this to your monitoring service
      // Examples: Sentry, LogRocket, Bugsnag, or your own error tracking API
      
      // Example for Sentry:
      // Sentry.captureException(error, {
      //   tags: { component: 'AppErrorBoundary' },
      //   extra: errorReport
      // });
      
      // Example for custom API:
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // });
      
      // Would send to monitoring service
    } catch (loggingError) {
      console.error('Failed to log error to monitoring service:', loggingError);
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      this.setState({ 
        hasError: false, 
        error: undefined, 
        errorInfo: undefined 
      });
      toast.info(`Retrying... (${this.retryCount}/${this.maxRetries})`);
    } else {
      toast.error('Maximum retry attempts reached. Please refresh the page.');
    }
  };

  private handleRefresh = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 max-w-lg w-full text-center border border-gray-200 dark:border-gray-700">
            <div className="mb-6">
              <AlertTriangle className="w-20 h-20 text-red-500 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Oops! Something went wrong
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                We're sorry, but a critical error occurred in the application. 
                This has been automatically reported to our team.
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Error ID: {this.state.errorId}
              </p>
            </div>
            
            <div className="space-y-3 mb-6">
              {this.retryCount < this.maxRetries && (
                <Button 
                  onClick={this.handleRetry} 
                  className="w-full"
                  size="lg"
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again ({this.maxRetries - this.retryCount} attempts left)
                </Button>
              )}
              
              <Button 
                variant="outline" 
                onClick={this.handleRefresh}
                className="w-full"
                size="lg"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh Page
              </Button>
              
              <Button 
                variant="secondary" 
                onClick={this.handleGoHome}
                className="w-full"
                size="lg"
              >
                <Home className="w-4 h-4 mr-2" />
                Go to Home
              </Button>
            </div>
            
            {/* Developer information in development mode */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  🔧 Development Error Details
                </summary>
                <div className="space-y-2">
                  <div>
                    <strong className="text-red-600 dark:text-red-400">Error:</strong>
                    <pre className="text-xs bg-red-50 dark:bg-red-900/20 p-2 rounded mt-1 overflow-auto">
                      {this.state.error.message}
                    </pre>
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <strong className="text-orange-600 dark:text-orange-400">Stack Trace:</strong>
                      <pre className="text-xs bg-orange-50 dark:bg-orange-900/20 p-2 rounded mt-1 overflow-auto max-h-32">
                        {this.state.error.stack}
                      </pre>
                    </div>
                  )}
                  {this.state.errorInfo?.componentStack && (
                    <div>
                      <strong className="text-blue-600 dark:text-blue-400">Component Stack:</strong>
                      <pre className="text-xs bg-blue-50 dark:bg-blue-900/20 p-2 rounded mt-1 overflow-auto max-h-32">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default AppErrorBoundary;
