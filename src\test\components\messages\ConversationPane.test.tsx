import React from 'react';
import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import ConversationPane from '@/components/messages/ConversationPane';
import { MOCK_IMAGES } from '@/lib/constants';

// Mock the child components
vi.mock('@/components/messages/ConversationHeader', () => ({
  default: ({ user }: { user: { name: string } }) => <div data-testid="conversation-header">{user.name}</div>
}));

vi.mock('@/components/MessageBubble', () => ({
  default: ({ message }: { message: { content: string } }) => <div data-testid="message-bubble">{message.content}</div>
}));

vi.mock('@/components/MessageComposer', () => ({
  default: ({ placeholder }: { placeholder: string }) => <div data-testid="message-composer">{placeholder}</div>
}));

const mockConversation = {
  id: '1',
  user: {
    name: '<PERSON>',
    avatar: MOCK_IMAGES.AVATARS[0],
    isOnline: true,
    lastActive: 'Active now'
  },
  lastMessage: {
    content: 'Hello there!',
    timestamp: '2h',
    isRead: false
  },
  unreadCount: 2
};

const mockMessages = [
  {
    id: '1',
    content: 'Hello!',
    timestamp: new Date(),
    senderId: 'currentUser',
    status: 'read' as const
  },
  {
    id: '2',
    content: 'Hi there!',
    timestamp: new Date(),
    senderId: '1',
    status: 'read' as const
  }
];

const defaultProps = {
  selectedConversation: mockConversation,
  messages: mockMessages,
  showConversation: true,
  isMobile: false,
  onBackToList: vi.fn(),
  onSendMessage: vi.fn()
};

describe('ConversationPane', () => {
  it('renders empty state when no conversation is selected on desktop', () => {
    render(
      <ConversationPane 
        {...defaultProps} 
        selectedConversation={null} 
        showConversation={false}
      />
    );
    
    expect(screen.getByText('Your Messages')).toBeInTheDocument();
    expect(screen.getByText('Select a conversation to start chatting')).toBeInTheDocument();
  });

  it('returns null when on mobile and not showing conversation', () => {
    const { container } = render(
      <ConversationPane 
        {...defaultProps} 
        selectedConversation={null} 
        showConversation={false}
        isMobile={true}
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  it('renders conversation header when conversation is selected', () => {
    render(<ConversationPane {...defaultProps} />);
    
    expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('renders messages when conversation is selected', () => {
    render(<ConversationPane {...defaultProps} />);
    
    const messageBubbles = screen.getAllByTestId('message-bubble');
    expect(messageBubbles).toHaveLength(2);
    expect(screen.getByText('Hello!')).toBeInTheDocument();
    expect(screen.getByText('Hi there!')).toBeInTheDocument();
  });

  it('renders message composer when conversation is selected', () => {
    render(<ConversationPane {...defaultProps} />);
    
    expect(screen.getByTestId('message-composer')).toBeInTheDocument();
    expect(screen.getByText('Type a message...')).toBeInTheDocument();
  });

  it('shows empty state when showConversation is false on desktop', () => {
    render(
      <ConversationPane 
        {...defaultProps} 
        showConversation={false}
      />
    );
    
    expect(screen.getByText('Your Messages')).toBeInTheDocument();
  });

  it('renders full conversation view when all conditions are met', () => {
    render(<ConversationPane {...defaultProps} />);
    
    // Should have header, messages, and composer
    expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    expect(screen.getAllByTestId('message-bubble')).toHaveLength(2);
    expect(screen.getByTestId('message-composer')).toBeInTheDocument();
  });

  it('handles empty messages array', () => {
    render(<ConversationPane {...defaultProps} messages={[]} />);
    
    // Should still render header and composer, but no messages
    expect(screen.getByTestId('conversation-header')).toBeInTheDocument();
    expect(screen.getByTestId('message-composer')).toBeInTheDocument();
    expect(screen.queryAllByTestId('message-bubble')).toHaveLength(0);
  });
});
