import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  <PERSON>,
  VideoOff,
  Mic,
  MicOff,
  PhoneOff,
  Users,
  MessageSquare,
  Settings,
  Monitor,
  MonitorOff,
  Circle,
  Square,
  Send,
  Grid3X3,
  Maximize2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { toast } from 'sonner';
import VideoCallService, { CallSession, CallParticipant, ChatMessage } from '@/services/VideoCallService';

interface EnhancedVideoCallProps {
  isOpen: boolean;
  onClose: () => void;
  callSession?: CallSession;
}

const EnhancedVideoCall: React.FC<EnhancedVideoCallProps> = ({
  isOpen,
  onClose,
  callSession: initialCallSession
}) => {
  const [callSession, setCallSession] = useState<CallSession | null>(initialCallSession || null);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [showParticipants, setShowParticipants] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [callDuration, setCallDuration] = useState(0);
  const [viewMode, setViewMode] = useState<'grid' | 'speaker' | 'gallery'>('grid');

  const localVideoRef = useRef<HTMLVideoElement>(null);
  const videoCallService = useRef(VideoCallService.getInstance());
  const callStartTime = useRef<Date | null>(null);
  const durationInterval = useRef<NodeJS.Timeout | null>(null);

  // Initialize video call service
  useEffect(() => {
    const service = videoCallService.current;

    const handleCallStarted = (session: CallSession) => {
      setCallSession(session);
      callStartTime.current = session.startTime;
      startDurationTimer();
    };

    const handleCallEnded = () => {
      setCallSession(null);
      callStartTime.current = null;
      stopDurationTimer();
      onClose();
    };

    const handleLocalStream = (stream: MediaStream) => {
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
    };

    const handleVideoToggled = (enabled: boolean) => {
      setIsVideoEnabled(enabled);
    };

    const handleAudioToggled = (enabled: boolean) => {
      setIsAudioEnabled(enabled);
    };

    const handleScreenShareStarted = () => {
      setIsScreenSharing(true);
      toast.success('Screen sharing started');
    };

    const handleScreenShareStopped = () => {
      setIsScreenSharing(false);
      toast.info('Screen sharing stopped');
    };

    const handleRecordingStarted = () => {
      setIsRecording(true);
      toast.success('Call recording started');
    };

    const handleRecordingStopped = (_url: string) => {
      setIsRecording(false);
      toast.success('Call recording saved');
      // You could automatically download or save the recording
    };

    const handleChatMessage = (message: ChatMessage) => {
      setChatMessages(prev => [...prev, message]);
      if (!showChat) {
        toast.info(`${message.senderName}: ${message.message}`);
      }
    };

    const handleParticipantJoined = (data: { userName: string; userId: string }) => {
      toast.info(`${data.userName} joined the call`);
    };

    const handleParticipantLeft = (data: { userName: string; userId: string }) => {
      toast.info(`${data.userName} left the call`);
    };

    // Subscribe to events
    service.on('call-started', handleCallStarted);
    service.on('call-ended', handleCallEnded);
    service.on('local-stream', handleLocalStream);
    service.on('video-toggled', handleVideoToggled);
    service.on('audio-toggled', handleAudioToggled);
    service.on('screen-share-started', handleScreenShareStarted);
    service.on('screen-share-stopped', handleScreenShareStopped);
    service.on('recording-started', handleRecordingStarted);
    service.on('recording-stopped', handleRecordingStopped);
    service.on('chat-message', handleChatMessage);
    service.on('participant-joined', handleParticipantJoined);
    service.on('participant-left', handleParticipantLeft);

    return () => {
      // Cleanup event listeners
      service.off('call-started', handleCallStarted);
      service.off('call-ended', handleCallEnded);
      service.off('local-stream', handleLocalStream);
      service.off('video-toggled', handleVideoToggled);
      service.off('audio-toggled', handleAudioToggled);
      service.off('screen-share-started', handleScreenShareStarted);
      service.off('screen-share-stopped', handleScreenShareStopped);
      service.off('recording-started', handleRecordingStarted);
      service.off('recording-stopped', handleRecordingStopped);
      service.off('chat-message', handleChatMessage);
      service.off('participant-joined', handleParticipantJoined);
      service.off('participant-left', handleParticipantLeft);
      stopDurationTimer();
    };
  }, [onClose, showChat, startDurationTimer, stopDurationTimer]);

  const startDurationTimer = useCallback(() => {
    durationInterval.current = setInterval(() => {
      if (callStartTime.current) {
        const duration = Math.floor((Date.now() - callStartTime.current.getTime()) / 1000);
        setCallDuration(duration);
      }
    }, 1000);
  }, []);

  const stopDurationTimer = useCallback(() => {
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
      durationInterval.current = null;
    }
  }, []);

  const formatDuration = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const handleToggleVideo = useCallback(() => {
    const enabled = videoCallService.current.toggleVideo();
    setIsVideoEnabled(enabled);
  }, []);

  const handleToggleAudio = useCallback(() => {
    const enabled = videoCallService.current.toggleAudio();
    setIsAudioEnabled(enabled);
  }, []);

  const handleToggleScreenShare = useCallback(async () => {
    try {
      if (isScreenSharing) {
        videoCallService.current.stopScreenShare();
      } else {
        await videoCallService.current.startScreenShare();
      }
    } catch {
      toast.error('Failed to toggle screen sharing');
    }
  }, [isScreenSharing]);

  const handleToggleRecording = useCallback(async () => {
    try {
      if (isRecording) {
        videoCallService.current.stopRecording();
      } else {
        await videoCallService.current.startRecording();
      }
    } catch {
      toast.error('Failed to toggle recording');
    }
  }, [isRecording]);

  const handleEndCall = useCallback(() => {
    videoCallService.current.endCall();
  }, []);

  const handleSendChatMessage = useCallback(() => {
    if (chatMessage.trim()) {
      videoCallService.current.sendChatMessage(chatMessage.trim());
      setChatMessage('');
    }
  }, [chatMessage]);

  const handleToggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  const renderParticipantVideo = (participant: CallParticipant, isLocal: boolean = false) => (
    <div
      key={participant.id}
      className={`relative rounded-lg overflow-hidden bg-gray-900 ${
        viewMode === 'speaker' && dominantSpeaker === participant.id
          ? 'col-span-2 row-span-2'
          : 'aspect-video'
      }`}
    >
      <video
        ref={isLocal ? localVideoRef : undefined}
        className="w-full h-full object-cover"
        autoPlay
        muted={isLocal}
        playsInline
      />
      
      {/* Participant info overlay */}
      <div className="absolute bottom-2 left-2 flex items-center space-x-2">
        <Avatar className="w-8 h-8">
          <AvatarImage src={participant.avatar} />
          <AvatarFallback>{participant.name[0]}</AvatarFallback>
        </Avatar>
        <span className="text-white text-sm font-medium bg-black/50 px-2 py-1 rounded">
          {participant.name}
        </span>
      </div>

      {/* Media status indicators */}
      <div className="absolute top-2 right-2 flex space-x-1">
        {!participant.isVideoEnabled && (
          <Badge variant="destructive" className="text-xs">
            <VideoOff className="w-3 h-3" />
          </Badge>
        )}
        {!participant.isAudioEnabled && (
          <Badge variant="destructive" className="text-xs">
            <MicOff className="w-3 h-3" />
          </Badge>
        )}
        {participant.isScreenSharing && (
          <Badge variant="secondary" className="text-xs">
            <Monitor className="w-3 h-3" />
          </Badge>
        )}
      </div>

      {/* Speaking indicator */}
      {dominantSpeaker === participant.id && (
        <div className="absolute inset-0 border-4 border-green-500 rounded-lg pointer-events-none" />
      )}
    </div>
  );

  if (!isOpen || !callSession) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl h-[90vh] p-0 overflow-hidden">
        <div className="flex flex-col h-full bg-gray-900 text-white">
          {/* Header */}
          <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
            <div className="flex items-center space-x-4">
              <h2 className="text-lg font-semibold">Video Call</h2>
              <Badge variant="secondary" className="text-xs">
                {formatDuration(callDuration)}
              </Badge>
              {isRecording && (
                <Badge variant="destructive" className="text-xs animate-pulse">
                <Circle className="w-4 h-4" />
                  Recording
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'speaker' : 'grid')}
              >
                <Grid3X3 className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleToggleFullscreen}
              >
                {isFullscreen ? <Minimize /> : <Maximize2 />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSettings(!showSettings)}
              >
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Main content area */}
          <div className="flex-1 flex">
            {/* Video area */}
            <div className="flex-1 p-4">
              <div className={`grid gap-4 h-full ${
                callSession.participants.length <= 2 
                  ? 'grid-cols-1 md:grid-cols-2'
                  : callSession.participants.length <= 4
                  ? 'grid-cols-2'
                  : 'grid-cols-3'
              }`}>
                {callSession.participants.map((participant, index) => 
                  renderParticipantVideo(participant, index === 0)
                )}
              </div>
            </div>

            {/* Chat sidebar */}
            {showChat && (
              <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
                <div className="p-4 border-b border-gray-700">
                  <h3 className="font-semibold">Chat</h3>
                </div>
                
                <div className="flex-1 overflow-y-auto p-4 space-y-3">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="flex space-x-2">
                      <Avatar className="w-6 h-6">
                        <AvatarFallback className="text-xs">
                          {message.senderName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium">{message.senderName}</span>
                          <span className="text-xs text-gray-400">
                            {message.timestamp.toLocaleTimeString()}
                          </span>
                        </div>
                        <p className="text-sm text-gray-300">{message.message}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="p-4 border-t border-gray-700">
                  <div className="flex space-x-2">
                    <Input
                      value={chatMessage}
                      onChange={(e) => setChatMessage(e.target.value)}
                      placeholder="Type a message..."
                      className="flex-1"
                      onKeyPress={(e) => e.key === 'Enter' && handleSendChatMessage()}
                    />
                    <Button size="sm" onClick={handleSendChatMessage}>
                      <Send className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Controls */}
          <div className="p-4 bg-gray-800 border-t border-gray-700">
            <div className="flex items-center justify-center space-x-4">
              <Button
                variant={isVideoEnabled ? "secondary" : "destructive"}
                size="lg"
                onClick={handleToggleVideo}
                className="rounded-full w-12 h-12"
              >
                {isVideoEnabled ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
              </Button>

              <Button
                variant={isAudioEnabled ? "secondary" : "destructive"}
                size="lg"
                onClick={handleToggleAudio}
                className="rounded-full w-12 h-12"
              >
                {isAudioEnabled ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
              </Button>

              <Button
                variant={isScreenSharing ? "default" : "secondary"}
                size="lg"
                onClick={handleToggleScreenShare}
                className="rounded-full w-12 h-12"
              >
                {isScreenSharing ? <MonitorOff className="w-5 h-5" /> : <Monitor className="w-5 h-5" />}
              </Button>

              <Button
                variant={isRecording ? "destructive" : "secondary"}
                size="lg"
                onClick={handleToggleRecording}
                className="rounded-full w-12 h-12"
              >
                {isRecording ? <Square className="w-5 h-5" /> : <Circle className="w-5 h-5" />}
              </Button>

              <Button
                variant="secondary"
                size="lg"
                onClick={() => setShowChat(!showChat)}
                className="rounded-full w-12 h-12"
              >
                <MessageSquare className="w-5 h-5" />
              </Button>

              <Button
                variant="secondary"
                size="lg"
                onClick={() => setShowParticipants(!showParticipants)}
                className="rounded-full w-12 h-12"
              >
                <Users className="w-5 h-5" />
              </Button>

              <Button
                variant="destructive"
                size="lg"
                onClick={handleEndCall}
                className="rounded-full w-12 h-12"
              >
                <PhoneOff className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EnhancedVideoCall;
