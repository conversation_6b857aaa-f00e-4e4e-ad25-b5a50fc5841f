// Enhanced Messaging Types with all Facebook features
export interface User {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastActive: string;
  lastSeen?: Date;
  isVerified?: boolean;
  username?: string;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file' | 'audio' | 'video' | 'sticker' | 'gif' | 'location' | 'poll' | 'story_reply';
  status: 'sending' | 'sent' | 'delivered' | 'read';
  replyTo?: string;
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
  mentions?: string[];
  isEdited?: boolean;
  editedAt?: Date;
  isDeleted?: boolean;
  deletedAt?: Date;
  isForwarded?: boolean;
  forwardedFrom?: string;
  threadReplies?: string[]; // Message IDs of thread replies
  isPinned?: boolean;
  metadata?: {
    linkPreview?: LinkPreview;
    location?: LocationData;
    poll?: PollData;
    storyReply?: StoryReplyData;
  };
}

export interface MessageReaction {
  id: string;
  emoji: string;
  userId: string;
  timestamp: Date;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'video' | 'gif' | 'sticker';
  url: string;
  name: string;
  size: number;
  thumbnail?: string;
  duration?: number; // For audio/video
  dimensions?: { width: number; height: number };
  mimeType?: string;
  isAnimated?: boolean;
}

export interface LinkPreview {
  url: string;
  title?: string;
  description?: string;
  image?: string;
  siteName?: string;
  favicon?: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
  address: string;
  name?: string;
}

export interface PollData {
  question: string;
  options: Array<{
    id: string;
    text: string;
    votes: string[]; // User IDs
  }>;
  allowMultiple: boolean;
  expiresAt?: Date;
}

export interface StoryReplyData {
  storyId: string;
  storyAuthorId: string;
  storyType: 'image' | 'video';
  storyThumbnail?: string;
}

export interface Conversation {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  user?: User; // For direct conversations
  participants?: User[]; // For group conversations
  lastMessage?: {
    content: string;
    timestamp: string;
    isRead: boolean;
    senderId: string;
  };
  unreadCount: number;
  mentionCount?: number;
  isTyping?: { [userId: string]: boolean };
  isMuted?: boolean;
  isPinned?: boolean;
  isArchived?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  settings?: ConversationSettings;
  permissions?: ConversationPermissions;
  theme?: ConversationTheme;
  pinnedMessages?: string[]; // Message IDs
  customEmoji?: string;
  nickname?: { [userId: string]: string };
  color?: string;
}

export interface ConversationSettings {
  deliveryReceipts: boolean;
  readReceipts: boolean;
  typingIndicators: boolean;
  sound: boolean;
  notifications: 'all' | 'mentions' | 'none';
  theme?: string;
  emoji?: string;
  nicknames?: { [userId: string]: string };
  disappearingMessages?: {
    enabled: boolean;
    duration: number; // in seconds
  };
}

export interface ConversationPermissions {
  canAddMembers: 'all' | 'admins' | 'none';
  canRemoveMembers: 'all' | 'admins' | 'none';
  canEditInfo: 'all' | 'admins' | 'none';
  canSendMessages: 'all' | 'admins' | 'none';
  canDeleteMessages: 'all' | 'admins' | 'none';
  requireAdminApproval: boolean;
}

export interface ConversationTheme {
  id: string;
  name: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
  };
  gradients?: {
    chat: string;
    composer: string;
  };
  emoji?: string;
}

export interface TypingIndicator {
  conversationId: string;
  userId: string;
  isTyping: boolean;
  timestamp: Date;
}

export interface FileAttachment {
  id: string;
  file: File;
  type: 'image' | 'file' | 'video' | 'audio';
  preview?: string;
  uploading?: boolean;
  progress?: number;
}

export interface CallData {
  id: string;
  conversationId: string;
  type: 'audio' | 'video';
  participants: string[];
  status: 'ringing' | 'active' | 'ended' | 'missed' | 'declined';
  startedAt?: Date;
  endedAt?: Date;
  duration?: number;
  initiatorId: string;
}

export interface VoiceMessage {
  id: string;
  url: string;
  duration: number;
  waveform?: number[];
  isPlaying?: boolean;
  currentTime?: number;
}

export interface MessageThread {
  parentMessageId: string;
  replies: Message[];
  participantCount: number;
  lastReplyAt: Date;
}

export interface MessageSearchResult {
  message: Message;
  conversationId: string;
  context: {
    before: Message[];
    after: Message[];
  };
  highlights: string[];
}

export interface ChatSettings {
  theme: 'system' | 'light' | 'dark';
  fontSize: 'small' | 'medium' | 'large';
  enterToSend: boolean;
  soundEnabled: boolean;
  notificationPreview: boolean;
  activeStatus: boolean;
  readReceipts: boolean;
  autoDownloadMedia: 'never' | 'wifi' | 'always';
  dataUsage: 'low' | 'medium' | 'high';
}

export interface EmojiData {
  id: string;
  emoji: string;
  name: string;
  category: string;
  keywords: string[];
  skinTones?: string[];
}

export interface StickerPack {
  id: string;
  name: string;
  description?: string;
  thumbnail: string;
  stickers: Sticker[];
  isPremium?: boolean;
  isOwned?: boolean;
}

export interface Sticker {
  id: string;
  packId: string;
  url: string;
  name?: string;
  keywords?: string[];
  animated?: boolean;
}

export interface QuickReply {
  id: string;
  text: string;
  category?: string;
  usageCount?: number;
}

export interface MessageDraft {
  conversationId: string;
  content: string;
  attachments?: FileAttachment[];
  replyTo?: string;
  timestamp: Date;
}
