import React, { useCallback, useEffect, useRef, useMemo, useState } from 'react';

// Debounce utility for performance optimization
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

// Throttle utility for performance optimization
export const throttle = <T extends (...args: unknown[]) => unknown>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

// Memory-efficient image loading with lazy loading
export const useImageLazyLoading = () => {
  const imageRefs = useRef<Map<string, HTMLImageElement>>(new Map());
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            const src = img.dataset.src;
            if (src) {
              img.src = src;
              img.removeAttribute('data-src');
              observerRef.current?.unobserve(img);
            }
          }
        });
      },
      { threshold: 0.1, rootMargin: '50px' }
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, []);

  const registerImage = useCallback((id: string, element: HTMLImageElement) => {
    imageRefs.current.set(id, element);
    observerRef.current?.observe(element);
  }, []);

  const unregisterImage = useCallback((id: string) => {
    const element = imageRefs.current.get(id);
    if (element) {
      observerRef.current?.unobserve(element);
      imageRefs.current.delete(id);
    }
  }, []);

  return { registerImage, unregisterImage };
};

// Virtual scrolling hook for large lists
export const useVirtualScrolling = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleRange = useMemo(() => {
    const start = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
    const visibleCount = Math.ceil(containerHeight / itemHeight);
    const end = Math.min(items.length, start + visibleCount + overscan * 2);
    return { start, end };
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan]);

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.start, visibleRange.end).map((item, index) => ({
      item,
      index: visibleRange.start + index,
      style: {
        position: 'absolute' as const,
        top: (visibleRange.start + index) * itemHeight,
        height: itemHeight,
        width: '100%'
      }
    }));
  }, [items, visibleRange, itemHeight]);

  const totalHeight = items.length * itemHeight;

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    const throttledHandler = throttle(() => {
      setScrollTop(event.currentTarget.scrollTop);
    }, 16); // ~60fps
    throttledHandler();
  }, []);

  return {
    visibleItems,
    totalHeight,
    handleScroll
  };
};

// Memory management utilities
export class MemoryManager {
  private static instance: MemoryManager;
  private cache = new Map<string, unknown>();
  private maxCacheSize = 100;
  private accessOrder = new Map<string, number>();

  static getInstance(): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager();
    }
    return MemoryManager.instance;
  }

  set(key: string, value: unknown): void {
    if (this.cache.size >= this.maxCacheSize) {
      this.evictLeastRecentlyUsed();
    }
    this.cache.set(key, value);
    this.accessOrder.set(key, Date.now());
  }

  get(key: string): unknown {
    if (this.cache.has(key)) {
      this.accessOrder.set(key, Date.now());
      return this.cache.get(key);
    }
    return null;
  }

  delete(key: string): void {
    this.cache.delete(key);
    this.accessOrder.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
  }

  private evictLeastRecentlyUsed(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, time] of this.accessOrder) {
      if (time < oldestTime) {
        oldestTime = time;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.delete(oldestKey);
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxCacheSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Bundle size optimization utilities
export const loadComponentAsync = <T extends React.ComponentType<Record<string, unknown>>>(
  importFunc: () => Promise<{ default: T }>
) => {
  return React.lazy(importFunc);
};

// Performance monitoring
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>();

  static startMeasure(name: string): void {
    performance.mark(`${name}-start`);
  }

  static endMeasure(name: string): number {
    performance.mark(`${name}-end`);
    performance.measure(name, `${name}-start`, `${name}-end`);
    
    const measure = performance.getEntriesByName(name, 'measure')[0];
    const duration = measure?.duration || 0;
    
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(duration);
    
    // Keep only last 100 measurements
    const measurements = this.metrics.get(name)!;
    if (measurements.length > 100) {
      measurements.shift();
    }
    
    return duration;
  }

  static getAverageTime(name: string): number {
    const measurements = this.metrics.get(name) || [];
    if (measurements.length === 0) return 0;
    
    const sum = measurements.reduce((acc, time) => acc + time, 0);
    return sum / measurements.length;
  }

  static getAllMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    
    for (const [name, measurements] of this.metrics) {
      const average = this.getAverageTime(name);
      const count = measurements.length;
      const latest = measurements[measurements.length - 1] || 0;
      
      result[name] = { average, count, latest };
    }
    
    return result;
  }

  static clearMetrics(): void {
    this.metrics.clear();
    performance.clearMarks();
    performance.clearMeasures();
  }
}

// React performance hooks
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const mountTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
  });

  useEffect(() => {
    PerformanceMonitor.startMeasure(`${componentName}-mount`);
    PerformanceMonitor.endMeasure(`${componentName}-mount`);
    
    return () => {
      PerformanceMonitor.startMeasure(`${componentName}-unmount`);
      PerformanceMonitor.endMeasure(`${componentName}-unmount`);
    };
  }, [componentName]);

  return {
    renderCount: renderCount.current,
    mountTime: mountTime.current
  };
};

// Optimized event handlers
export const useOptimizedCallback = <T extends (...args: unknown[]) => unknown>(
  callback: T,
  deps: React.DependencyList,
  delay?: number
): T => {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const optimizedCallback = useCallback(callback, deps);
  
  return useMemo(() => {
    if (delay) {
      return debounce(optimizedCallback, delay) as T;
    }
    return optimizedCallback;
  }, [optimizedCallback, delay]);
};

// Memory leak prevention
export const useCleanup = (cleanup: () => void) => {
  useEffect(() => {
    return cleanup;
  }, [cleanup]);
};

// Image optimization utilities
export const optimizeImageUrl = (
  url: string, 
  width?: number, 
  height?: number, 
  quality: number = 80
): string => {
  if (!url) return '';
  
  // For external URLs, return as-is (in production, you'd use a service like Cloudinary)
  if (url.startsWith('http')) {
    return url;
  }
  
  // For local images, add optimization parameters
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (height) params.set('h', height.toString());
  params.set('q', quality.toString());
  
  return `${url}?${params.toString()}`;
};

// Bundle analyzer helper
export const analyzeBundleSize = () => {
  if (typeof window !== 'undefined' && 'performance' in window) {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    return {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      totalLoadTime: navigation.loadEventEnd - navigation.fetchStart,
      transferSize: navigation.transferSize,
      encodedBodySize: navigation.encodedBodySize,
      decodedBodySize: navigation.decodedBodySize
    };
  }
  
  return null;
};

export default {
  debounce,
  throttle,
  useImageLazyLoading,
  useVirtualScrolling,
  MemoryManager,
  PerformanceMonitor,
  usePerformanceMonitor,
  useOptimizedCallback,
  useCleanup,
  optimizeImageUrl,
  analyzeBundleSize
};
