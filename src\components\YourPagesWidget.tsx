import React from 'react';
import { FileText, Users, TrendingUp, Setting<PERSON>, Plus } from 'lucide-react';

interface Page {
  id: string;
  name: string;
  avatar: string;
  category: string;
  followers: number;
  newNotifications: number;
  isVerified?: boolean;
}

const YourPagesWidget: React.FC = () => {
  const pages: Page[] = [
    {
      id: '1',
      name: 'Tech Innovations Hub',
      avatar: '/placeholder.svg',
      category: 'Technology',
      followers: 15420,
      newNotifications: 5,
      isVerified: true,
    },
    {
      id: '2',
      name: 'Local Coffee Shop',
      avatar: '/placeholder.svg',
      category: 'Restaurant',
      followers: 892,
      newNotifications: 2,
    },
    {
      id: '3',
      name: 'Photography Studio',
      avatar: '/placeholder.svg',
      category: 'Art & Photography',
      followers: 3240,
      newNotifications: 0,
    },
  ];

  const formatFollowers = (count: number): string => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <FileText className="w-4 h-4" />
          Your Pages
        </h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          See All
        </button>
      </div>
      
      <div className="space-y-3">
        {pages.map((page) => (
          <div key={page.id} className="flex items-center gap-3 p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors cursor-pointer group">
            <div className="relative">
              <img
                src={page.avatar}
                alt={page.name}
                className="w-10 h-10 rounded-lg object-cover"
              />
              {page.isVerified && (
                <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                  <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {page.name}
                </p>
                {page.newNotifications > 0 && (
                  <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 min-w-[1.25rem] h-5 flex items-center justify-center">
                    {page.newNotifications}
                  </span>
                )}
              </div>
              
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">
                {page.category}
              </p>
              
              <div className="flex items-center justify-between mt-1">
                <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                  <Users className="w-3 h-3" />
                  <span>{formatFollowers(page.followers)} followers</span>
                </div>
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <button className="p-1 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors">
                    <TrendingUp className="w-3 h-3" />
                  </button>
                  <button className="p-1 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors">
                    <Settings className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full mt-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors flex items-center justify-center gap-2">
        <Plus className="w-4 h-4" />
        Create New Page
      </button>
    </div>
  );
};

export default YourPagesWidget;