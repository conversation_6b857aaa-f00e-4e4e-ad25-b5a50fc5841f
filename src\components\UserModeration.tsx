import React, { useState, useCallback } from 'react';
import {
  Shield,
  Ban,
  Flag,
  AlertTriangle,
  User,
  MessageSquare,
  Share,
  Zap,
  Lock,
  Eye,
  EyeOff,
  Check
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

export interface ReportReason {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'spam' | 'harassment' | 'inappropriate' | 'violence' | 'fake' | 'intellectual_property';
}

export interface BlockSettings {
  blockMessages: boolean;
  blockPosts: boolean;
  blockComments: boolean;
  blockTags: boolean;
  blockInvites: boolean;
  hideFromSearch: boolean;
  blockFriendRequests: boolean;
  blockPageInvites: boolean;
  blockGroupInvites: boolean;
  blockEventInvites: boolean;
}

export interface User {
  id: string;
  name: string;
  avatar: string;
  username?: string;
  mutualFriends?: number;
  isVerified?: boolean;
  isBlocked?: boolean;
}

interface UserModerationProps {
  user: User;
  contentType?: 'profile' | 'post' | 'comment' | 'message' | 'story';
  contentId?: string;
  isOpen: boolean;
  onClose: () => void;
  onAction: (action: 'block' | 'unblock' | 'report', data?: Record<string, unknown>) => void;
  showBlockOptions?: boolean;
  showReportOptions?: boolean;
}

const REPORT_REASONS: ReportReason[] = [
  {
    id: 'spam',
    label: 'Spam',
    description: 'Repetitive or unwanted content',
    icon: <Zap className="w-4 h-4" />,
    severity: 'medium',
    category: 'spam'
  },
  {
    id: 'harassment',
    label: 'Harassment or Bullying',
    description: 'Targeted harassment, bullying, or threats',
    icon: <AlertTriangle className="w-4 h-4" />,
    severity: 'high',
    category: 'harassment'
  },
  {
    id: 'inappropriate_content',
    label: 'Inappropriate Content',
    description: 'Sexual, violent, or disturbing content',
    icon: <EyeOff className="w-4 h-4" />,
    severity: 'high',
    category: 'inappropriate'
  },
  {
    id: 'hate_speech',
    label: 'Hate Speech',
    description: 'Content that attacks based on identity',
    icon: <Ban className="w-4 h-4" />,
    severity: 'critical',
    category: 'harassment'
  },
  {
    id: 'violence',
    label: 'Violence or Dangerous Organizations',
    description: 'Promotes violence or dangerous activities',
    icon: <Shield className="w-4 h-4" />,
    severity: 'critical',
    category: 'violence'
  },
  {
    id: 'fake_account',
    label: 'Fake Account',
    description: 'Impersonation or fake identity',
    icon: <User className="w-4 h-4" />,
    severity: 'medium',
    category: 'fake'
  },
  {
    id: 'intellectual_property',
    label: 'Intellectual Property Violation',
    description: 'Copyright or trademark infringement',
    icon: <Lock className="w-4 h-4" />,
    severity: 'medium',
    category: 'intellectual_property'
  },
  {
    id: 'false_information',
    label: 'False Information',
    description: 'Misinformation or fake news',
    icon: <Flag className="w-4 h-4" />,
    severity: 'high',
    category: 'fake'
  }
];

const DEFAULT_BLOCK_SETTINGS: BlockSettings = {
  blockMessages: true,
  blockPosts: true,
  blockComments: true,
  blockTags: true,
  blockInvites: true,
  hideFromSearch: true,
  blockFriendRequests: true,
  blockPageInvites: true,
  blockGroupInvites: true,
  blockEventInvites: true
};

const UserModeration: React.FC<UserModerationProps> = ({
  user,
  contentType = 'profile',
  contentId,
  isOpen,
  onClose,
  onAction,
  showBlockOptions = true,
  showReportOptions = true
}) => {
  const [activeTab, setActiveTab] = useState<'report' | 'block'>('report');
  const [selectedReason, setSelectedReason] = useState<string>('');
  const [reportDescription, setReportDescription] = useState('');
  const [blockSettings, setBlockSettings] = useState<BlockSettings>(DEFAULT_BLOCK_SETTINGS);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [blockDuration, setBlockDuration] = useState<'permanent' | 'temporary'>('permanent');
  const [blockDays, setBlockDays] = useState('30');
  const [additionalEvidence, setAdditionalEvidence] = useState({
    screenshots: false,
    messages: false,
    otherPosts: false
  });

  const handleReport = useCallback(async () => {
    if (!selectedReason) {
      toast.error('Please select a reason for reporting');
      return;
    }

    setIsSubmitting(true);
    
    try {
      const reportData = {
        userId: user.id,
        reason: selectedReason,
        description: reportDescription,
        contentType,
        contentId,
        evidence: additionalEvidence,
        timestamp: new Date().toISOString()
      };

      await onAction('report', reportData);
      
      toast.success('Report submitted successfully');
      onClose();
      
      // Reset form
      setSelectedReason('');
      setReportDescription('');
      setAdditionalEvidence({
        screenshots: false,
        messages: false,
        otherPosts: false
      });
    } catch {
      toast.error('Failed to submit report');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedReason, reportDescription, user.id, contentType, contentId, additionalEvidence, onAction, onClose]);

  const handleBlock = useCallback(async () => {
    setIsSubmitting(true);
    
    try {
      const blockData = {
        userId: user.id,
        settings: blockSettings,
        duration: blockDuration,
        days: blockDuration === 'temporary' ? parseInt(blockDays) : undefined,
        timestamp: new Date().toISOString()
      };

      await onAction('block', blockData);
      
      toast.success(`${user.name} has been blocked`);
      onClose();
    } catch {
      toast.error('Failed to block user');
    } finally {
      setIsSubmitting(false);
    }
  }, [user.id, user.name, blockSettings, blockDuration, blockDays, onAction, onClose]);

  const handleUnblock = useCallback(async () => {
    setIsSubmitting(true);
    
    try {
      await onAction('unblock', { userId: user.id });
      toast.success(`${user.name} has been unblocked`);
      onClose();
    } catch {
      toast.error('Failed to unblock user');
    } finally {
      setIsSubmitting(false);
    }
  }, [user.id, user.name, onAction, onClose]);

  const updateBlockSetting = useCallback((key: keyof BlockSettings, value: boolean) => {
    setBlockSettings(prev => ({ ...prev, [key]: value }));
  }, []);

  const selectedReasonData = REPORT_REASONS.find(r => r.id === selectedReason);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900/30';
      case 'critical': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/30';
    }
  };

  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'post': return 'Post';
      case 'comment': return 'Comment';
      case 'message': return 'Message';
      case 'story': return 'Story';
      default: return 'Profile';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-3">
            <Shield className="w-5 h-5" />
            <div>
              <div className="flex items-center space-x-2">
                <span>Report or Block User</span>
                {user.isVerified && (
                  <Badge variant="secondary" className="text-xs">
                    <Check className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              <p className="text-sm text-gray-500 font-normal">
                {getContentTypeLabel()} by {user.name}
              </p>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* User Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={user.avatar} />
                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <h3 className="font-semibold">{user.name}</h3>
                    {user.isVerified && (
                      <Check className="w-4 h-4 text-blue-500" />
                    )}
                    {user.isBlocked && (
                      <Badge variant="destructive" className="text-xs">Blocked</Badge>
                    )}
                  </div>
                  {user.username && (
                    <p className="text-sm text-gray-500">@{user.username}</p>
                  )}
                  {user.mutualFriends && (
                    <p className="text-xs text-gray-500">{user.mutualFriends} mutual friends</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Tabs */}
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            {showReportOptions && (
              <button
                onClick={() => setActiveTab('report')}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors flex-1 justify-center',
                  activeTab === 'report'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                )}
              >
                <Flag className="w-4 h-4" />
                <span>Report</span>
              </button>
            )}
            {showBlockOptions && (
              <button
                onClick={() => setActiveTab('block')}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors flex-1 justify-center',
                  activeTab === 'block'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                )}
              >
                <Ban className="w-4 h-4" />
                <span>{user.isBlocked ? 'Unblock' : 'Block'}</span>
              </button>
            )}
          </div>

          {/* Tab Content */}
          <AnimatePresence mode="wait">
            {activeTab === 'report' && (
              <motion.div
                key="report"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Why are you reporting this {getContentTypeLabel().toLowerCase()}?</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <RadioGroup value={selectedReason} onValueChange={setSelectedReason}>
                      {REPORT_REASONS.map((reason) => (
                        <div key={reason.id} className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value={reason.id} id={reason.id} />
                            <Label htmlFor={reason.id} className="flex items-center space-x-2 cursor-pointer">
                              {reason.icon}
                              <span className="font-medium">{reason.label}</span>
                              <Badge className={cn('text-xs', getSeverityColor(reason.severity))}>
                                {reason.severity}
                              </Badge>
                            </Label>
                          </div>
                          <p className="text-sm text-gray-500 ml-6">{reason.description}</p>
                        </div>
                      ))}
                    </RadioGroup>

                    {selectedReason && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                      >
                        <div>
                          <Label htmlFor="description" className="text-sm font-medium">
                            Additional details (optional)
                          </Label>
                          <Textarea
                            id="description"
                            placeholder="Please provide any additional context or details..."
                            value={reportDescription}
                            onChange={(e) => setReportDescription(e.target.value)}
                            className="mt-1"
                            rows={3}
                          />
                        </div>

                        <div>
                          <Label className="text-sm font-medium mb-3 block">
                            Do you have additional evidence?
                          </Label>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="screenshots"
                                checked={additionalEvidence.screenshots}
                                onCheckedChange={(checked) => 
                                  setAdditionalEvidence(prev => ({ ...prev, screenshots: checked as boolean }))
                                }
                              />
                              <Label htmlFor="screenshots" className="text-sm">Screenshots</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="messages"
                                checked={additionalEvidence.messages}
                                onCheckedChange={(checked) => 
                                  setAdditionalEvidence(prev => ({ ...prev, messages: checked as boolean }))
                                }
                              />
                              <Label htmlFor="messages" className="text-sm">Message history</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="otherPosts"
                                checked={additionalEvidence.otherPosts}
                                onCheckedChange={(checked) => 
                                  setAdditionalEvidence(prev => ({ ...prev, otherPosts: checked as boolean }))
                                }
                              />
                              <Label htmlFor="otherPosts" className="text-sm">Other posts or content</Label>
                            </div>
                          </div>
                        </div>

                        {selectedReasonData && (
                          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                            <p className="text-sm text-yellow-800 dark:text-yellow-200">
                              <strong>What happens next:</strong> Our team will review your report within 24 hours. 
                              {selectedReasonData.severity === 'critical' && 
                                ' Due to the serious nature of this report, it will be prioritized for immediate review.'
                              }
                            </p>
                          </div>
                        )}
                      </motion.div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {activeTab === 'block' && (
              <motion.div
                key="block"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="space-y-4"
              >
                {user.isBlocked ? (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <Ban className="w-12 h-12 text-red-500 mx-auto mb-4" />
                      <h3 className="font-semibold text-lg mb-2">User is currently blocked</h3>
                      <p className="text-gray-500 mb-4">
                        {user.name} is blocked and cannot interact with you on Facebook.
                      </p>
                      <Button
                        onClick={handleUnblock}
                        disabled={isSubmitting}
                        variant="outline"
                        className="border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
                      >
                        {isSubmitting ? 'Unblocking...' : 'Unblock User'}
                      </Button>
                    </CardContent>
                  </Card>
                ) : (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Block {user.name}</CardTitle>
                      <p className="text-sm text-gray-500">
                        Choose what you want to prevent this user from doing
                      </p>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium mb-3 block">Block Duration</Label>
                        <RadioGroup value={blockDuration} onValueChange={(value: 'permanent' | 'temporary') => setBlockDuration(value)}>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="permanent" id="permanent" />
                            <Label htmlFor="permanent">Permanent</Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <RadioGroupItem value="temporary" id="temporary" />
                            <Label htmlFor="temporary">Temporary</Label>
                          </div>
                        </RadioGroup>

                        {blockDuration === 'temporary' && (
                          <div className="mt-3">
                            <Label htmlFor="blockDays" className="text-sm">Duration (days)</Label>
                            <Select value={blockDays} onValueChange={setBlockDays}>
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="1">1 day</SelectItem>
                                <SelectItem value="7">1 week</SelectItem>
                                <SelectItem value="30">1 month</SelectItem>
                                <SelectItem value="90">3 months</SelectItem>
                                <SelectItem value="365">1 year</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        )}
                      </div>

                      <Separator />

                      <div>
                        <Label className="text-sm font-medium mb-3 block">Block Settings</Label>
                        <div className="space-y-3">
                          {[
                            { key: 'blockMessages', label: 'Block messages', icon: <MessageSquare className="w-4 h-4" /> },
                            { key: 'blockPosts', label: 'Hide posts from your feed', icon: <Eye className="w-4 h-4" /> },
                            { key: 'blockComments', label: 'Block comments on your posts', icon: <MessageSquare className="w-4 h-4" /> },
                            { key: 'blockTags', label: 'Prevent tagging you', icon: <User className="w-4 h-4" /> },
                            { key: 'blockFriendRequests', label: 'Block friend requests', icon: <User className="w-4 h-4" /> },
                            { key: 'hideFromSearch', label: 'Hide from search results', icon: <EyeOff className="w-4 h-4" /> },
                            { key: 'blockInvites', label: 'Block all invites', icon: <Share className="w-4 h-4" /> }
                          ].map((setting) => (
                            <div key={setting.key} className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                {setting.icon}
                                <Label className="text-sm">{setting.label}</Label>
                              </div>
                              <Checkbox
                                checked={blockSettings[setting.key as keyof BlockSettings]}
                                onCheckedChange={(checked) => 
                                  updateBlockSetting(setting.key as keyof BlockSettings, checked as boolean)
                                }
                              />
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                        <p className="text-sm text-red-800 dark:text-red-200">
                          <strong>Note:</strong> When you block someone, they won't be notified. 
                          You can unblock them at any time from your blocked users list in settings.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            
            {activeTab === 'report' && !user.isBlocked && (
              <Button 
                onClick={handleReport} 
                disabled={!selectedReason || isSubmitting}
                className="bg-red-600 hover:bg-red-700"
              >
                {isSubmitting ? 'Submitting...' : 'Submit Report'}
              </Button>
            )}
            
            {activeTab === 'block' && !user.isBlocked && (
              <Button 
                onClick={handleBlock} 
                disabled={isSubmitting}
                variant="destructive"
              >
                {isSubmitting ? 'Blocking...' : 'Block User'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserModeration;
