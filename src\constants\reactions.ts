export interface Reaction {
  emoji: string;
  label: string;
  id: string;
  color: string;
}

export const REACTION_TYPES: Reaction[] = [
  { emoji: '👍', label: 'Like', id: 'like', color: 'text-blue-500' },
  { emoji: '❤️', label: 'Love', id: 'love', color: 'text-red-500' },
  { emoji: '😂', label: 'Haha', id: 'haha', color: 'text-yellow-500' },
  { emoji: '😮', label: 'Wow', id: 'wow', color: 'text-orange-500' },
  { emoji: '😢', label: 'Sad', id: 'sad', color: 'text-blue-400' },
  { emoji: '😡', label: 'Angry', id: 'angry', color: 'text-red-600' }
];
