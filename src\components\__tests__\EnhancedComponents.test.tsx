import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { toast } from 'sonner';

// Import components to test
import UnifiedPostCard from '@/components/shared/UnifiedPostCard';
import UnifiedSearch from '@/components/shared/UnifiedSearch';
import AdvancedReactionSystem from '@/components/social/AdvancedReactionSystem';

import LazyLoadManager from '@/components/optimization/LazyLoadManager';
import { BasePost } from '@/types/shared';

// Mock external dependencies
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.ComponentProps<'div'>) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: React.ComponentProps<'button'>) => <button {...props}>{children}</button>
  },
  AnimatePresence: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

vi.mock('date-fns', () => ({
  formatDistanceToNow: vi.fn(() => '2 hours ago'),
  format: vi.fn(() => 'Jan 15, 2024'),
  isAfter: vi.fn(() => true),
  isBefore: vi.fn(() => false),
  startOfDay: vi.fn(() => new Date()),
  endOfDay: vi.fn(() => new Date())
}));

// Mock storage
const mockStorage = {
  get: vi.fn(() => []),
  set: vi.fn(),
  remove: vi.fn()
};

vi.mock('@/lib/storage', () => ({
  storage: mockStorage
}));

// Test data
const mockPost: BasePost = {
  id: '1',
  user_id: 'user1',
  content: 'This is a test post',
  image_url: 'https://example.com/image.jpg',
  created_at: '2024-01-15T10:00:00Z',
  updated_at: '2024-01-15T10:00:00Z',
  profiles: {
    id: 'user1',
    full_name: 'John Doe',
    avatar_url: 'https://example.com/avatar.jpg'
  },
  likes_count: 5,
  comments_count: 3,
  user_has_liked: false
};

describe('UnifiedPostCard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders post content correctly', () => {
    render(<UnifiedPostCard post={mockPost} />);
    
    expect(screen.getByText('This is a test post')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument(); // likes count
  });

  it('handles like interaction', async () => {
    const user = userEvent.setup();
    const onInteraction = vi.fn();
    
    render(
      <UnifiedPostCard 
        post={mockPost} 
        onInteraction={onInteraction}
      />
    );
    
    const likeButton = screen.getByRole('button', { name: /like/i });
    await user.click(likeButton);
    
    expect(onInteraction).toHaveBeenCalledWith('1', 'like', expect.any(Object));
    expect(toast.success).toHaveBeenCalledWith('Post liked');
  });

  it('handles save interaction', async () => {
    const user = userEvent.setup();
    const onInteraction = vi.fn();
    
    render(
      <UnifiedPostCard 
        post={mockPost} 
        onInteraction={onInteraction}
      />
    );
    
    const saveButton = screen.getByRole('button', { name: /bookmark/i });
    await user.click(saveButton);
    
    expect(onInteraction).toHaveBeenCalledWith('1', 'save', expect.any(Object));
    expect(mockStorage.set).toHaveBeenCalled();
  });

  it('shows comments section when toggled', async () => {
    const user = userEvent.setup();
    
    render(<UnifiedPostCard post={mockPost} showComments={true} />);
    
    const commentButton = screen.getByRole('button', { name: /comment/i });
    await user.click(commentButton);
    
    expect(screen.getByPlaceholderText('Write a comment...')).toBeInTheDocument();
  });

  it('handles comment submission', async () => {
    const user = userEvent.setup();
    const onInteraction = vi.fn();
    
    render(
      <UnifiedPostCard 
        post={mockPost} 
        onInteraction={onInteraction}
        showComments={true}
      />
    );
    
    // Open comments section
    const commentButton = screen.getByRole('button', { name: /comment/i });
    await user.click(commentButton);
    
    // Type and submit comment
    const commentInput = screen.getByPlaceholderText('Write a comment...');
    await user.type(commentInput, 'This is a test comment');
    
    const submitButton = screen.getByRole('button', { name: /post/i });
    await user.click(submitButton);
    
    expect(onInteraction).toHaveBeenCalledWith('1', 'comment', expect.any(Object));
    expect(toast.success).toHaveBeenCalledWith('Comment added');
  });

  it('renders in compact variant', () => {
    render(<UnifiedPostCard post={mockPost} variant="compact" />);
    
    // Compact variant should still show essential content
    expect(screen.getByText('This is a test post')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});

describe('UnifiedSearch', () => {
  const mockOnResultSelect = vi.fn();
  const mockOnClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders search interface when open', () => {
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={mockOnClose}
        onResultSelect={mockOnResultSelect}
      />
    );
    
    expect(screen.getByPlaceholderText(/search for people/i)).toBeInTheDocument();
    expect(screen.getByText('All')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    render(
      <UnifiedSearch 
        isOpen={false}
        onClose={mockOnClose}
        onResultSelect={mockOnResultSelect}
      />
    );
    
    expect(screen.queryByPlaceholderText(/search for people/i)).not.toBeInTheDocument();
  });

  it('handles search input', async () => {
    const user = userEvent.setup();
    
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={mockOnClose}
        onResultSelect={mockOnResultSelect}
      />
    );
    
    const searchInput = screen.getByPlaceholderText(/search for people/i);
    await user.type(searchInput, 'test query');
    
    expect(searchInput).toHaveValue('test query');
  });

  it('shows trending searches when no query', () => {
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={mockOnClose}
        onResultSelect={mockOnResultSelect}
        showTrending={true}
      />
    );
    
    expect(screen.getByText('Trending')).toBeInTheDocument();
  });

  it('handles category filter changes', async () => {
    const user = userEvent.setup();
    
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={mockOnClose}
        onResultSelect={mockOnResultSelect}
      />
    );
    
    const peopleTab = screen.getByRole('tab', { name: /people/i });
    await user.click(peopleTab);
    
    // Should filter to people category
    expect(peopleTab).toHaveAttribute('data-state', 'active');
  });
});

describe('AdvancedReactionSystem', () => {
  const mockOnReactionChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders reaction button', () => {
    render(
      <AdvancedReactionSystem 
        postId="1"
        currentUserId="user1"
        onReactionChange={mockOnReactionChange}
      />
    );
    
    expect(screen.getByRole('button', { name: /react/i })).toBeInTheDocument();
  });

  it('shows reaction picker when clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <AdvancedReactionSystem 
        postId="1"
        currentUserId="user1"
        onReactionChange={mockOnReactionChange}
      />
    );
    
    const reactButton = screen.getByRole('button', { name: /react/i });
    await user.click(reactButton);
    
    // Should show reaction options
    expect(screen.getByTitle('Like')).toBeInTheDocument();
    expect(screen.getByTitle('Love')).toBeInTheDocument();
  });

  it('handles reaction selection', async () => {
    const user = userEvent.setup();
    
    render(
      <AdvancedReactionSystem 
        postId="1"
        currentUserId="user1"
        onReactionChange={mockOnReactionChange}
      />
    );
    
    // Open reaction picker
    const reactButton = screen.getByRole('button', { name: /react/i });
    await user.click(reactButton);
    
    // Select love reaction
    const loveButton = screen.getByTitle('Love');
    await user.click(loveButton);
    
    expect(mockOnReactionChange).toHaveBeenCalledWith('1', expect.any(Array));
    expect(toast.success).toHaveBeenCalledWith('Reacted with ❤️ Love');
  });

  it('displays reaction summary', () => {
    const initialReactions = [
      {
        userId: 'user2',
        userName: 'Jane Doe',
        userAvatar: 'https://example.com/jane.jpg',
        reaction: {
          id: 'like',
          emoji: '👍',
          name: 'Like',
          color: '#1877f2'
        },
        timestamp: new Date()
      }
    ];
    
    render(
      <AdvancedReactionSystem 
        postId="1"
        currentUserId="user1"
        initialReactions={initialReactions}
        onReactionChange={mockOnReactionChange}
      />
    );
    
    expect(screen.getByText('1 reaction')).toBeInTheDocument();
  });
});

describe('LazyLoadManager', () => {
  it('renders loading fallback initially', () => {
    render(<LazyLoadManager type="newsfeed" />);
    
    // Should show loading state
    expect(screen.getByTestId('loading-spinner') || screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('handles unknown component type gracefully', () => {
    render(<LazyLoadManager type="unknown" as any />);
    
    expect(screen.getByText('Unknown component type')).toBeInTheDocument();
  });

  it('uses custom fallback when provided', () => {
    const CustomFallback = () => <div>Custom Loading...</div>;
    
    render(<LazyLoadManager type="newsfeed" fallback={CustomFallback} />);
    
    expect(screen.getByText('Custom Loading...')).toBeInTheDocument();
  });
});

describe('Performance and Accessibility', () => {
  it('UnifiedPostCard has proper ARIA labels', () => {
    render(<UnifiedPostCard post={mockPost} />);
    
    const likeButton = screen.getByRole('button', { name: /like/i });
    expect(likeButton).toBeInTheDocument();
    
    const shareButton = screen.getByRole('button', { name: /share/i });
    expect(shareButton).toBeInTheDocument();
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    
    render(<UnifiedPostCard post={mockPost} />);
    
    const likeButton = screen.getByRole('button', { name: /like/i });
    
    // Focus and activate with keyboard
    likeButton.focus();
    expect(likeButton).toHaveFocus();
    
    await user.keyboard('{Enter}');
    expect(toast.success).toHaveBeenCalled();
  });

  it('maintains focus management in search', async () => {
    userEvent.setup();
    
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={vi.fn()}
        onResultSelect={vi.fn()}
      />
    );
    
    const searchInput = screen.getByPlaceholderText(/search for people/i);
    expect(searchInput).toHaveFocus();
  });
});

describe('Error Handling', () => {
  it('handles missing post data gracefully', () => {
    const invalidPost = { ...mockPost, profiles: null };
    
    render(<UnifiedPostCard post={invalidPost} />);
    
    expect(screen.getByText('Anonymous User')).toBeInTheDocument();
  });

  it('handles network errors in search', async () => {
    const user = userEvent.setup();
    
    // Mock console.error to avoid test output noise
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(
      <UnifiedSearch 
        isOpen={true}
        onClose={vi.fn()}
        onResultSelect={vi.fn()}
      />
    );
    
    const searchInput = screen.getByPlaceholderText(/search for people/i);
    await user.type(searchInput, 'test');
    
    // Should handle search gracefully even if there are errors
    expect(searchInput).toHaveValue('test');
    
    consoleSpy.mockRestore();
  });
});

describe('Integration Tests', () => {
  it('post card and reaction system work together', async () => {
    const user = userEvent.setup();
    const onInteraction = vi.fn();
    
    render(
      <div>
        <UnifiedPostCard post={mockPost} onInteraction={onInteraction} />
        <AdvancedReactionSystem 
          postId={mockPost.id}
          currentUserId="user1"
          onReactionChange={vi.fn()}
        />
      </div>
    );
    
    // Interact with post card
    const likeButton = screen.getAllByRole('button', { name: /like/i })[0];
    await user.click(likeButton);
    
    expect(onInteraction).toHaveBeenCalled();
    expect(toast.success).toHaveBeenCalled();
  });
});
