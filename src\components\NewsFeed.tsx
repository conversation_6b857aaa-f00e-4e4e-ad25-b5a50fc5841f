import React, { memo, useMemo } from 'react';
import { UnifiedPostCard } from '@/components/shared';

interface Post {
  id: string;
  user_id: string;
  content: string;
  image_url?: string;
  created_at: string;
  updated_at: string;
  profiles: {
    id: string;
    full_name?: string;
    avatar_url?: string;
  } | null;
  likes_count?: number;
  comments_count?: number;
  user_has_liked?: boolean;
  reactions?: Record<string, number>;
  feeling?: string;
  location?: string;
  tagged_friends?: string[];
  privacy?: string;
  is_live?: boolean;
  isPoll?: boolean;
  pollOptions?: string[];
  pollVotes?: Record<string, number>;
}

interface NewsFeedProps {
  posts: Post[];
  onPostInteraction?: (postId: string, action: string) => void;
}

const NewsFeed: React.FC<NewsFeedProps> = memo(({ posts, onPostInteraction }) => {
  // Memoize the posts rendering to prevent unnecessary re-renders
  const renderedPosts = useMemo(() => {
    return posts.map((post, index) => (
      <UnifiedPostCard
        key={post.id}
        post={post}
        onInteraction={onPostInteraction}
        priority={index < 3} // Prioritize first 3 posts for faster loading
        variant="default"
        showActions={true}
        showComments={true}
      />
    ));
  }, [posts, onPostInteraction]);

  return (
    <div className="space-y-4">
      {renderedPosts}
    </div>
  );
});

NewsFeed.displayName = 'NewsFeed';

export default NewsFeed;
