/**
 * WebRTC Signal Types for Enhanced Type Safety
 * Replacing 'any' types with proper TypeScript interfaces
 */

export interface BaseSignal {
  type: string;
  userId: string;
  timestamp: number;
  data?: Record<string, unknown>;
}

export interface CallRequestSignal extends BaseSignal {
  type: 'call-request';
  conversationId: string;
  callType: 'audio' | 'video';
  from: string;
  data?: {
    metadata?: {
      userName?: string;
      avatar?: string;
    };
  };
}

export interface CallAcceptedSignal extends BaseSignal {
  type: 'call-accepted';
  data: {
    sessionId: string;
  };
}

export interface CallRejectedSignal extends BaseSignal {
  type: 'call-rejected';
  data: {
    reason?: string;
  };
}

export interface CallEndedSignal extends BaseSignal {
  type: 'call-ended';
  data: {
    reason?: 'user-ended' | 'timeout' | 'error';
  };
}

export interface IceCandidateSignal extends BaseSignal {
  type: 'ice-candidate';
  data: {
    candidate: RTCIceCandidate;
  };
}

export interface OfferSignal extends BaseSignal {
  type: 'offer';
  data: {
    offer: RTCSessionDescriptionInit;
  };
}

export interface AnswerSignal extends BaseSignal {
  type: 'answer';
  data: {
    answer: RTCSessionDescriptionInit;
  };
}

export type WebRTCSignal = 
  | CallRequestSignal
  | CallAcceptedSignal 
  | CallRejectedSignal
  | CallEndedSignal
  | IceCandidateSignal
  | OfferSignal
  | AnswerSignal;

export interface EncryptedMessage {
  id: string;
  encryptedContent: string;
  content?: string;
  conversationId?: string;
  iv: string;
  timestamp: number;
  from: string;
  to: string;
  messageType: 'text' | 'image' | 'file' | 'voice';
}

export interface MessageData {
  id: string;
  content: string;
  timestamp: number;
  from: string;
  to: string;
  type: 'message' | 'typing' | 'read-receipt' | 'delivery-receipt';
  metadata?: Record<string, unknown>;
}
