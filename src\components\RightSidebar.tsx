import React from 'react';
import { memo } from 'react';
import QuickActions from '@/components/QuickActions';
import ActivityFeed from '@/components/ActivityFeed';
import FundraiserWidget from '@/components/FundraiserWidget';
import FriendRequestsPanel from '@/components/FriendRequestsPanel';
import PeopleYouMayKnow from '@/components/PeopleYouMayKnow';
import BirthdayWidget from '@/components/BirthdayWidget';
import EventsWidget from '@/components/EventsWidget';
import TrendingTopics from '@/components/TrendingTopics';
import GroupSuggestions from '@/components/GroupSuggestions';
import ContactsWidget from '@/components/ContactsWidget';
import GroupConversationsWidget from '@/components/GroupConversationsWidget';
import YourPagesWidget from '@/components/YourPagesWidget';
import ProfilesWidget from '@/components/ProfilesWidget';
import { useIsMobile } from '@/hooks/use-device';

// Optimize RightSidebar with memoization to prevent unnecessary re-renders
const RightSidebar = memo(() => {
  const isMobile = useIsMobile();
  
  // Don't render on mobile as it's handled differently in the layout
  if (isMobile) return null;

  return (
    <div className="w-full space-y-4 sticky top-20 h-[calc(100vh-5rem)] overflow-y-auto pb-6 pr-1 scrollbar-thin">
      {/* Quick Actions */}
      <QuickActions />
      
      {/* Activity Feed */}
      <ActivityFeed />
      
      {/* Contacts */}
      <ContactsWidget />
      
      {/* Group Conversations */}
      <GroupConversationsWidget />
      
      {/* Your Pages */}
      <YourPagesWidget />
      
      {/* Profiles */}
      <ProfilesWidget />
      
      {/* Friend Requests */}
      <FriendRequestsPanel />
      
      {/* People You May Know */}
      <PeopleYouMayKnow />
      
      {/* Birthday Widget */}
      <BirthdayWidget />

      {/* Events Widget */}
      <EventsWidget />
      
      {/* Trending Topics */}
      <TrendingTopics />
      
      {/* Group Suggestions */}
      <GroupSuggestions />
      
      {/* Fundraiser Widget */}
      <FundraiserWidget />
    </div>
  );
});

RightSidebar.displayName = 'RightSidebar';

export default RightSidebar;