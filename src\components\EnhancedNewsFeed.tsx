import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  Heart, 
  MessageCircle, 
  Share2, 
  MoreHorizontal,
  Bookmark,
  Flag,
  Users,
  Globe,
  Lock,
  EyeOff
} from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import { motion, AnimatePresence } from 'framer-motion';
import SocialFeaturesService, { Post, User, Comment } from '@/services/SocialFeaturesService';
import EmojiPicker from './EmojiPicker';
import ReactionPicker from './ReactionPicker';
import OptimizedImage from './OptimizedImage';

interface PostAction {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  onClick: (post: Post) => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

interface NewsFeedProps {
  userId?: string;
  className?: string;
  maxPosts?: number;
  showCreatePost?: boolean;
  filter?: 'all' | 'friends' | 'pages' | 'groups';
  sortBy?: 'recent' | 'relevant' | 'top';
}

const EnhancedNewsFeed: React.FC<NewsFeedProps> = ({
  userId,
  className = '',
  maxPosts = 20,
  _showCreatePost = true,
  _filter = 'all', // TODO: Implement filtering functionality
  sortBy = 'recent'
}) => {
  const [posts, setPosts] = useState<Post[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [showComments, setShowComments] = useState<Record<string, boolean>>({});
  const [commentText, setCommentText] = useState<Record<string, string>>({});
  const [showReactionPicker, setShowReactionPicker] = useState<Record<string, boolean>>({});
  const [showShareDialog, setShowShareDialog] = useState<Post | null>(null);
  const [shareComment, setShareComment] = useState('');
  const [expandedPosts, setExpandedPosts] = useState<Record<string, boolean>>({});
  const [currentUser, setCurrentUser] = useState<User | null>(null);

  const socialService = useMemo(() => SocialFeaturesService.getInstance(), []);

  // Memoize sorting functions to avoid recreating on every render
  const sortingFunctions = useMemo(() => ({
    relevant: (a: Post, b: Post) => {
      const scoreA = Object.values(a.reactions).flat().length + a.comments.length + a.shares.length;
      const scoreB = Object.values(b.reactions).flat().length + b.comments.length + b.shares.length;
      return scoreB - scoreA;
    },
    top: (a: Post, b: Post) => {
      const reactionsA = Object.values(a.reactions).flat().length;
      const reactionsB = Object.values(b.reactions).flat().length;
      return reactionsB - reactionsA;
    },
    recent: () => 0 // Default sorting (already sorted by date)
  }), []);

  // Memoize filtered and sorted posts
  const processedPosts = useMemo(() => {
    try {
      let allPosts = userId ? socialService.getUserPosts(userId) : socialService.getPosts();

      // Apply sorting
      if (sortBy !== 'recent') {
        allPosts = [...allPosts].sort(sortingFunctions[sortBy]);
      }

      return allPosts.slice(0, maxPosts);
    } catch (error) {
      console.error('Error processing posts:', error);
      return [];
    }
  }, [socialService, userId, maxPosts, sortBy, sortingFunctions]);

  useEffect(() => {
    setLoading(true);
    try {
      const user = socialService.getCurrentUser();
      setCurrentUser(user);
      setPosts(processedPosts);
    } catch (error) {
      console.error('Error loading posts:', error);
      toast.error('Failed to load posts');
    } finally {
      setLoading(false);
    }
  }, [socialService, processedPosts]);

  const handleReaction = useCallback((postId: string, reactionType: string) => {
    try {
      socialService.reactToPost(postId, reactionType);
      
      // Update local state
      setPosts(prev => prev.map(post => {
        if (post.id === postId) {
          const updatedPost = { ...post };
          
          // Remove existing reaction from current user
          Object.keys(updatedPost.reactions).forEach(type => {
            updatedPost.reactions[type] = updatedPost.reactions[type].filter(
              user => user.id !== currentUser?.id
            );
          });
          
          // Add new reaction
          if (!updatedPost.reactions[reactionType]) {
            updatedPost.reactions[reactionType] = [];
          }
          if (currentUser) {
            updatedPost.reactions[reactionType].push(currentUser);
          }
          
          return updatedPost;
        }
        return post;
      }));
      
      setShowReactionPicker(prev => ({ ...prev, [postId]: false }));
    } catch (error) {
      console.error('Error reacting to post:', error);
      toast.error('Failed to react to post');
    }
  }, [socialService, currentUser]);

  const handleComment = useCallback((postId: string) => {
    const text = commentText[postId]?.trim();
    if (!text) return;

    try {
      socialService.commentOnPost(postId, text);
      
      // Update local state
      setPosts(prev => prev.map(post => {
        if (post.id === postId && currentUser) {
          const newComment: Comment = {
            id: `comment-${Date.now()}`,
            author: currentUser,
            content: text,
            createdAt: new Date(),
            updatedAt: new Date(),
            reactions: {},
            replies: []
          };
          
          return {
            ...post,
            comments: [...post.comments, newComment]
          };
        }
        return post;
      }));
      
      setCommentText(prev => ({ ...prev, [postId]: '' }));
      toast.success('Comment added');
    } catch (error) {
      console.error('Error commenting on post:', error);
      toast.error('Failed to add comment');
    }
  }, [commentText, socialService, currentUser]);

  const handleShare = useCallback((post: Post) => {
    try {
      socialService.sharePost(post.id, shareComment);
      setShowShareDialog(null);
      setShareComment('');
      toast.success('Post shared');
    } catch (error) {
      console.error('Error sharing post:', error);
      toast.error('Failed to share post');
    }
  }, [socialService, shareComment]);

  const toggleComments = useCallback((postId: string) => {
    setShowComments(prev => ({ ...prev, [postId]: !prev[postId] }));
  }, []);

  const toggleExpanded = useCallback((postId: string) => {
    setExpandedPosts(prev => ({ ...prev, [postId]: !prev[postId] }));
  }, []);

  const getReactionCount = useCallback((post: Post) => {
    return Object.values(post.reactions).flat().length;
  }, []);

  const getUserReaction = useCallback((post: Post) => {
    if (!currentUser) return null;
    
    for (const [reactionType, users] of Object.entries(post.reactions)) {
      if (users.some(user => user.id === currentUser.id)) {
        return reactionType;
      }
    }
    return null;
  }, [currentUser]);

  const getPrivacyIcon = useCallback((privacy: string) => {
    switch (privacy) {
      case 'public':
        return <Globe className="w-3 h-3" />;
      case 'friends':
        return <Users className="w-3 h-3" />;
      case 'only_me':
        return <Lock className="w-3 h-3" />;
      default:
        return <Users className="w-3 h-3" />;
    }
  }, []);

  const postActions: PostAction[] = useMemo(() => [
    {
      id: 'save',
      label: 'Save post',
      icon: Bookmark,
      onClick: (_post) => {
        toast.success('Post saved');
      }
    },
    {
      id: 'hide',
      label: 'Hide post',
      icon: EyeOff,
      onClick: (post) => {
        setPosts(prev => prev.filter(p => p.id !== post.id));
        toast.info('Post hidden');
      }
    },
    {
      id: 'report',
      label: 'Report post',
      icon: Flag,
      onClick: (_post) => {
        toast.info('Post reported');
      },
      variant: 'destructive'
    }
  ], []);

  const renderPostContent = useCallback((post: Post) => {
    const isExpanded = expandedPosts[post.id];
    const shouldTruncate = post.content.length > 300;
    const displayContent = shouldTruncate && !isExpanded 
      ? post.content.substring(0, 300) + '...'
      : post.content;

    return (
      <div className="space-y-3">
        {/* Text Content */}
        {post.content && (
          <div className="text-sm text-gray-900 dark:text-gray-100">
            <p className="whitespace-pre-wrap">{displayContent}</p>
            {shouldTruncate && (
              <Button
                variant="link"
                size="sm"
                className="p-0 h-auto text-blue-600 hover:text-blue-800"
                onClick={() => toggleExpanded(post.id)}
              >
                {isExpanded ? 'See less' : 'See more'}
              </Button>
            )}
          </div>
        )}

        {/* Media */}
        {post.media && post.media.length > 0 && (
          <div className={`grid gap-2 ${
            post.media.length === 1 ? 'grid-cols-1' :
            post.media.length === 2 ? 'grid-cols-2' :
            post.media.length === 3 ? 'grid-cols-2' :
            'grid-cols-2'
          }`}>
            {post.media.map((media, index) => (
              <div key={media.id} className={`relative ${
                post.media.length === 3 && index === 0 ? 'row-span-2' : ''
              }`}>
                {media.type === 'image' ? (
                  <OptimizedImage
                    src={media.url}
                    alt={media.alt || `Media ${index + 1}`}
                    className="w-full h-auto rounded-lg object-cover"
                    loading="lazy"
                  />
                ) : media.type === 'video' ? (
                  <video
                    src={media.url}
                    controls
                    className="w-full h-auto rounded-lg"
                    poster={media.thumbnail}
                  />
                ) : null}
                
                {/* Photo Tags */}
                {media.tags && media.tags.map((tag, tagIndex) => (
                  <div
                    key={tagIndex}
                    className="absolute bg-black/70 text-white text-xs px-2 py-1 rounded"
                    style={{
                      left: `${tag.x}%`,
                      top: `${tag.y}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                  >
                    {tag.user.name}
                  </div>
                ))}
              </div>
            ))}
          </div>
        )}

        {/* Location */}
        {post.location && (
          <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
            <span>📍</span>
            <span>{post.location.name}</span>
          </div>
        )}

        {/* Feeling/Activity */}
        {post.feeling && (
          <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
            <span>😊</span>
            <span>feeling {post.feeling}</span>
          </div>
        )}

        {/* Tagged Users */}
        {post.tags && post.tags.length > 0 && (
          <div className="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400">
            <span>with</span>
            {post.tags.map((user, index) => (
              <span key={user.id}>
                <Button variant="link" className="p-0 h-auto text-blue-600 hover:text-blue-800">
                  {user.name}
                </Button>
                {index < post.tags.length - 1 && ', '}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  }, [expandedPosts, toggleExpanded]);

  const renderReactionSummary = useCallback((post: Post) => {
    const totalReactions = getReactionCount(post);
    if (totalReactions === 0) return null;

    const topReactions = Object.entries(post.reactions)
      .filter(([_, users]) => users.length > 0)
      .sort(([_, a], [__, b]) => b.length - a.length)
      .slice(0, 3);

    return (
      <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <div className="flex items-center space-x-2">
          <div className="flex -space-x-1">
            {topReactions.map(([reaction]) => (
              <div key={reaction} className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center text-xs">
                {reaction === 'like' ? '👍' : 
                 reaction === 'love' ? '❤️' :
                 reaction === 'haha' ? '😂' :
                 reaction === 'wow' ? '😮' :
                 reaction === 'sad' ? '😢' :
                 reaction === 'angry' ? '😡' : '👍'}
              </div>
            ))}
          </div>
          <span>
            {totalReactions} {totalReactions === 1 ? 'reaction' : 'reactions'}
          </span>
        </div>
        
        <div className="flex items-center space-x-4">
          {post.comments.length > 0 && (
            <Button
              variant="link"
              size="sm"
              className="p-0 h-auto text-gray-600 hover:text-gray-800"
              onClick={() => toggleComments(post.id)}
            >
              {post.comments.length} {post.comments.length === 1 ? 'comment' : 'comments'}
            </Button>
          )}
          {post.shares.length > 0 && (
            <span>{post.shares.length} {post.shares.length === 1 ? 'share' : 'shares'}</span>
          )}
        </div>
      </div>
    );
  }, [getReactionCount, toggleComments]);

  if (loading) {
    return (
      <div className="space-y-4">
        {Array.from({ length: 3 }).map((_, index) => (
          <Card key={index} className="w-full">
            <CardContent className="p-4">
              <div className="animate-pulse space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gray-300 rounded-full" />
                  <div className="space-y-2 flex-1">
                    <div className="h-4 bg-gray-300 rounded w-1/4" />
                    <div className="h-3 bg-gray-300 rounded w-1/6" />
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded" />
                  <div className="h-4 bg-gray-300 rounded w-3/4" />
                </div>
                <div className="h-40 bg-gray-300 rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <AnimatePresence>
        {posts.map((post) => {
          const userReaction = getUserReaction(post);
          
          return (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="w-full bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700">
                <CardContent className="p-0">
                  {/* Post Header */}
                  <div className="p-4 pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-10 h-10">
                          <AvatarImage src={post.author.avatar} />
                          <AvatarFallback>{post.author.name[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                              {post.author.name}
                            </h3>
                            {post.author.verification?.isVerified && (
                              <Badge variant="secondary" className="text-xs">
                                ✓
                              </Badge>
                            )}
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                            <span>{formatDistanceToNow(post.createdAt, { addSuffix: true })}</span>
                            <span>•</span>
                            <div className="flex items-center space-x-1">
                              {getPrivacyIcon(post.privacy)}
                              <span className="capitalize">{post.privacy}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      {/* Post Actions Menu */}
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => setSelectedPost(selectedPost === post ? null : post)}
                        >
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                        
                        {selectedPost?.id === post.id && (
                          <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-10 min-w-48">
                            {postActions.map((action) => (
                              <Button
                                key={action.id}
                                variant="ghost"
                                size="sm"
                                className="w-full justify-start text-left"
                                onClick={() => {
                                  action.onClick(post);
                                  setSelectedPost(null);
                                }}
                              >
                                <action.icon className="w-4 h-4 mr-2" />
                                {action.label}
                              </Button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Post Content */}
                  <div className="px-4 pb-3">
                    {renderPostContent(post)}
                  </div>

                  {/* Reaction Summary */}
                  {getReactionCount(post) > 0 && (
                    <div className="px-4 pb-2">
                      {renderReactionSummary(post)}
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-2">
                    <div className="flex items-center justify-around">
                      {/* Like Button */}
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          className={`flex items-center space-x-2 ${
                            userReaction ? 'text-blue-600' : 'text-gray-600'
                          }`}
                          onClick={() => setShowReactionPicker(prev => ({ 
                            ...prev, 
                            [post.id]: !prev[post.id] 
                          }))}
                        >
                          <Heart className={`w-4 h-4 ${userReaction === 'love' ? 'fill-current text-red-500' : ''}`} />
                          <span className="text-sm">{userReaction || 'Like'}</span>
                        </Button>
                        
                        {showReactionPicker[post.id] && (
                          <div className="absolute bottom-full left-0 mb-2 z-20">
                            <ReactionPicker
                              onReaction={(reaction) => handleReaction(post.id, reaction)}
                              onClose={() => setShowReactionPicker(prev => ({ ...prev, [post.id]: false }))}
                            />
                          </div>
                        )}
                      </div>

                      {/* Comment Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center space-x-2 text-gray-600"
                        onClick={() => toggleComments(post.id)}
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span className="text-sm">Comment</span>
                      </Button>

                      {/* Share Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="flex items-center space-x-2 text-gray-600"
                        onClick={() => setShowShareDialog(post)}
                      >
                        <Share2 className="w-4 h-4" />
                        <span className="text-sm">Share</span>
                      </Button>
                    </div>
                  </div>

                  {/* Comments Section */}
                  {showComments[post.id] && (
                    <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-3 space-y-3">
                      {/* Comment Input */}
                      <div className="flex items-start space-x-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={currentUser?.avatar} />
                          <AvatarFallback>{currentUser?.name[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-2">
                          <Textarea
                            placeholder="Write a comment..."
                            value={commentText[post.id] || ''}
                            onChange={(e) => setCommentText(prev => ({ 
                              ...prev, 
                              [post.id]: e.target.value 
                            }))}
                            className="min-h-[60px] resize-none"
                          />
                          <div className="flex justify-between">
                            <div className="flex items-center space-x-2">
                              <EmojiPicker
                                onEmojiSelect={(emoji) => setCommentText(prev => ({ 
                                  ...prev, 
                                  [post.id]: (prev[post.id] || '') + emoji 
                                }))}
                              />
                            </div>
                            <Button
                              size="sm"
                              onClick={() => handleComment(post.id)}
                              disabled={!commentText[post.id]?.trim()}
                            >
                              Post
                            </Button>
                          </div>
                        </div>
                      </div>

                      {/* Comments List */}
                      <div className="space-y-3">
                        {post.comments.map((comment) => (
                          <div key={comment.id} className="flex items-start space-x-3">
                            <Avatar className="w-8 h-8">
                              <AvatarImage src={comment.author.avatar} />
                              <AvatarFallback>{comment.author.name[0]}</AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                              <div className="bg-gray-100 dark:bg-gray-700 rounded-lg px-3 py-2">
                                <p className="font-semibold text-sm text-gray-900 dark:text-gray-100">
                                  {comment.author.name}
                                </p>
                                <p className="text-sm text-gray-800 dark:text-gray-200">
                                  {comment.content}
                                </p>
                              </div>
                              <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                                <span>{formatDistanceToNow(comment.createdAt, { addSuffix: true })}</span>
                                <Button variant="link" size="sm" className="p-0 h-auto text-xs">
                                  Like
                                </Button>
                                <Button variant="link" size="sm" className="p-0 h-auto text-xs">
                                  Reply
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Share Dialog */}
      {showShareDialog && (
        <Dialog open={!!showShareDialog} onOpenChange={() => setShowShareDialog(null)}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Share Post</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <Textarea
                placeholder="Say something about this..."
                value={shareComment}
                onChange={(e) => setShareComment(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setShowShareDialog(null)}>
                  Cancel
                </Button>
                <Button onClick={() => handleShare(showShareDialog)}>
                  Share
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default EnhancedNewsFeed;
