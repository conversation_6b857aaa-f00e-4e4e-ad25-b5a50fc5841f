import React from 'react';

// Higher-order component for image optimization
export function withImageOptimization<P extends object>(
  Component: React.ComponentType<P>
) {
  return React.memo((props: P) => {
    return <Component {...props} />;
  });
}

// Image preloader utility
export const preloadImages = (urls: string[], options?: {
  width?: number;
  height?: number;
  quality?: number;
}) => {
  return Promise.all(
    urls.map(url => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();

        if (options?.width) img.width = options.width;
        if (options?.height) img.height = options.height;

        img.onload = () => resolve();
        img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
        img.src = url;
      });
    })
  );
};

// Image cache manager
export class ImageCache {
  private static cache = new Map<string, HTMLImageElement>();
  private static maxSize = 50;

  static get(url: string): HTMLImageElement | null {
    return this.cache.get(url) || null;
  }

  static set(url: string, img: HTMLImageElement): void {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(url, img);
  }

  static clear(): void {
    this.cache.clear();
  }

  static getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      urls: Array.from(this.cache.keys())
    };
  }
}

// Image format detection utility
export const getOptimalImageFormat = (): 'webp' | 'avif' | 'jpeg' => {
  if (typeof window === 'undefined') return 'jpeg';
  
  const canvas = document.createElement('canvas');
  canvas.width = 1;
  canvas.height = 1;
  
  // Check for AVIF support
  if (canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0) {
    return 'avif';
  }
  
  // Check for WebP support
  if (canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0) {
    return 'webp';
  }
  
  return 'jpeg';
};

// Image compression utility
export const compressImage = (
  file: File,
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
    format?: 'jpeg' | 'webp' | 'png';
  } = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }
    
    img.onload = () => {
      const { maxWidth = 1920, maxHeight = 1080, quality = 0.8, format = 'jpeg' } = options;
      
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        `image/${format}`,
        quality
      );
    };
    
    img.onerror = () => reject(new Error('Failed to load image for compression'));
    img.src = URL.createObjectURL(file);
  });
};
