import React, { useState } from 'react';
import { Plus, Camera, Video, Calendar, Users, Store, MessageCircle, Heart, Bookmark, Share, Radio, Film, Music, Gift, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { ROUTES } from '@/lib/constants';
import EnhancedCheckIn, { CheckInData } from './EnhancedCheckIn';

const QuickActions = () => {
  const navigate = useNavigate();
  const [isCheckInOpen, setIsCheckInOpen] = useState(false);

  const handleCheckIn = (checkInData: CheckInData) => {
    // Handle the check-in data - could save to local storage, send to API, etc.
    console.log('Check-in data:', checkInData);
    toast.success(`✅ Checked in at ${checkInData.location.name}!`);
  };

  const actions = [
    {
      icon: Plus,
      label: 'Create Post',
      color: 'text-blue-600',
      bgColor: 'hover:bg-blue-50 dark:hover:bg-blue-900/20',
      action: () => {
        navigate(ROUTES.HOME);
        toast.success('📝 Opening post creator');
      }
    },
    {
      icon: MapPin,
      label: 'Check In',
      color: 'text-emerald-600',
      bgColor: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/20',
      action: () => {
        setIsCheckInOpen(true);
      }
    },
    {
      icon: Camera,
      label: 'Add Photo',
      color: 'text-green-600',
      bgColor: 'hover:bg-green-50 dark:hover:bg-green-900/20',
      action: () => {
        // Simulate file picker
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = () => toast.success('📸 Photo selected for upload!');
        input.click();
      }
    },
    {
      icon: Video,
      label: 'Add Video',
      color: 'text-purple-600',
      bgColor: 'hover:bg-purple-50 dark:hover:bg-purple-900/20',
      action: () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'video/*';
        input.onchange = () => toast.success('🎥 Video selected for upload!');
        input.click();
      }
    },
    {
      icon: Radio,
      label: 'Go Live',
      color: 'text-red-600',
      bgColor: 'hover:bg-red-50 dark:hover:bg-red-900/20',
      action: () => {
        navigate('/live');
        toast.success('🔴 Starting live stream...');
      }
    },
    {
      icon: Film,
      label: 'Create Reel',
      color: 'text-pink-600',
      bgColor: 'hover:bg-pink-50 dark:hover:bg-pink-900/20',
      action: () => {
        navigate(ROUTES.REELS);
        toast.success('🎬 Opening Reels creator');
      }
    },
    {
      icon: Calendar,
      label: 'Create Event',
      color: 'text-purple-600',
      bgColor: 'hover:bg-purple-50 dark:hover:bg-purple-900/20',
      action: () => {
        navigate(ROUTES.EVENTS);
        toast.success('📅 Creating new event');
      }
    },
    {
      icon: Users,
      label: 'Create Group',
      color: 'text-orange-600',
      bgColor: 'hover:bg-orange-50 dark:hover:bg-orange-900/20',
      action: () => {
        navigate(ROUTES.GROUPS);
        toast.success('👥 Creating new group');
      }
    },
    {
      icon: Store,
      label: 'Sell Item',
      color: 'text-indigo-600',
      bgColor: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/20',
      action: () => {
        navigate(ROUTES.MARKETPLACE);
        toast.success('🛒 Creating marketplace listing');
      }
    },
    {
      icon: MessageCircle,
      label: 'Send Message',
      color: 'text-blue-500',
      bgColor: 'hover:bg-blue-50 dark:hover:bg-blue-900/20',
      action: () => {
        navigate(ROUTES.MESSAGES);
        toast.success('💬 Opening messages');
      }
    },
    {
      icon: Music,
      label: 'Share Music',
      color: 'text-emerald-600',
      bgColor: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/20',
      action: () => {
        toast.success('🎵 Music sharing feature coming soon!');
      }
    },
    {
      icon: Gift,
      label: 'Send Gift',
      color: 'text-rose-600',
      bgColor: 'hover:bg-rose-50 dark:hover:bg-rose-900/20',
      action: () => {
        toast.success('🎁 Gift sending feature coming soon!');
      }
    },
    {
      icon: Share,
      label: 'Share Link',
      color: 'text-gray-600',
      bgColor: 'hover:bg-gray-50 dark:hover:bg-gray-800',
      action: () => {
        navigator.clipboard.writeText(window.location.href);
        toast.success('🔗 Page link copied to clipboard!');
      }
    }
  ];

  return (
    <>
      <Card className="hidden lg:block">
        <CardHeader className="p-3">
          <CardTitle className="text-base font-semibold flex items-center">
            <Plus className="w-4 h-4 mr-2 text-blue-600" />
            Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent className="p-2">
          <div className="grid grid-cols-2 gap-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                onClick={action.action}
                className={`h-auto py-3 flex-col gap-2 transition-all duration-200 ${action.bgColor} border border-transparent hover:border-gray-200 dark:hover:border-gray-700 rounded-lg`}
              >
                <action.icon className={`w-5 h-5 ${action.color}`} />
                <span className="text-xs text-gray-700 dark:text-gray-300 font-medium text-center leading-tight">
                  {action.label}
                </span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      <EnhancedCheckIn
        isOpen={isCheckInOpen}
        onClose={() => setIsCheckInOpen(false)}
        onCheckIn={handleCheckIn}
      />
    </>
  );
};

export default QuickActions;