import { MarketplaceListing } from '@/services/SocialFeaturesService';
import { MarketplaceFilters, SELLER_RESPONSE_TEMPLATES, INITIAL_NEW_LISTING } from '@/constants/marketplace';

/**
 * Generates automated seller response based on user message content
 */
export const getSellerResponse = (userMessage: string): string => {
  const lowerMessage = userMessage.toLowerCase();

  if (lowerMessage.includes('available') || lowerMessage.includes('still for sale')) {
    return SELLER_RESPONSE_TEMPLATES.available;
  } else if (lowerMessage.includes('price') || lowerMessage.includes('cost')) {
    return SELLER_RESPONSE_TEMPLATES.price;
  } else if (lowerMessage.includes('condition') || lowerMessage.includes('quality')) {
    return SELLER_RESPONSE_TEMPLATES.condition;
  } else if (lowerMessage.includes('meet') || lowerMessage.includes('pickup')) {
    return SELLER_RESPONSE_TEMPLATES.meetup;
  } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
    return SELLER_RESPONSE_TEMPLATES.greeting;
  } else {
    return SELLER_RESPONSE_TEMPLATES.default;
  }
};

/**
 * Validates listing form data
 */
export const validateListingForm = (listing: typeof INITIAL_NEW_LISTING): string | null => {
  if (!listing.title.trim()) return 'Title is required';
  if (!listing.price || parseFloat(listing.price) <= 0) return 'Valid price is required';
  if (!listing.category) return 'Category is required';
  return null;
};

/**
 * Optimized filtering and sorting function for marketplace listings
 */
export const filterListings = (
  listings: MarketplaceListing[],
  searchQuery: string,
  filters: MarketplaceFilters
): MarketplaceListing[] => {
  const filtered = listings.filter(listing => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        listing.title.toLowerCase().includes(query) ||
        listing.description.toLowerCase().includes(query) ||
        listing.category.toLowerCase().includes(query);
      if (!matchesSearch) return false;
    }

    // Category filter
    if (filters.category !== 'all' && listing.category !== filters.category) {
      return false;
    }

    // Condition filter
    if (filters.condition !== 'all' && listing.condition !== filters.condition) {
      return false;
    }

    // Price filter
    if (listing.price < filters.priceMin || listing.price > filters.priceMax) {
      return false;
    }

    // Location filter
    if (filters.location) {
      const query = filters.location.toLowerCase();
      const matchesLocation = 
        listing.location.name.toLowerCase().includes(query) ||
        listing.location.address?.toLowerCase().includes(query);
      if (!matchesLocation) return false;
    }

    // Delivery options filter
    if (filters.deliveryOptions.length > 0) {
      const hasDeliveryOption = filters.deliveryOptions.some(option => 
        listing.deliveryOptions.includes(option)
      );
      if (!hasDeliveryOption) return false;
    }

    return true;
  });

  // Sort
  switch (filters.sortBy) {
    case 'price_low':
      filtered.sort((a, b) => a.price - b.price);
      break;
    case 'price_high':
      filtered.sort((a, b) => b.price - a.price);
      break;
    case 'recent':
    default:
      filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      break;
  }

  return filtered;
};

/**
 * Formats price with currency symbol
 */
export const formatPrice = (price: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

/**
 * Checks if a listing was created within the last 24 hours
 */
export const isRecentListing = (createdAt: Date): boolean => {
  const now = new Date();
  const diffInHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
  return diffInHours < 24;
};
