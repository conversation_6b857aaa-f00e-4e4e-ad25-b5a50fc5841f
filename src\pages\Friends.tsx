import React, { useEffect } from 'react';
import FriendsTab from '@/components/FriendsTab';
import { useLocation } from 'react-router-dom';
import { toast } from 'sonner';

const Friends = () => {
  const location = useLocation();

  // Handle navigation from notifications
  useEffect(() => {
    if (location.state?.fromNotification) {
      const { highlightUser } = location.state;
      
      if (highlightUser) {
        toast.success(`👥 Viewing friend request from user: ${highlightUser}`);
        console.log('👥 Friend request notification for:', highlightUser);
      } else {
        toast.success('👥 Opening friends from notification');
      }
      
      // Clear the state to prevent repeated toasts
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location]);

  return (
    <div className="w-full">
      <FriendsTab />
    </div>
  );
};

export default Friends;