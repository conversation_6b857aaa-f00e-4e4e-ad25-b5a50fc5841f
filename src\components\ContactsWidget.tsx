import React from 'react';
import { Users, MessageCircle, Phone, Video } from 'lucide-react';

interface Contact {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastSeen?: string;
}

const ContactsWidget: React.FC = () => {
  const contacts: Contact[] = [
    {
      id: '1',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      isOnline: true,
    },
    {
      id: '2',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      isOnline: true,
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      isOnline: false,
      lastSeen: '2 hours ago',
    },
    {
      id: '4',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      isOnline: true,
    },
    {
      id: '5',
      name: '<PERSON>',
      avatar: '/placeholder.svg',
      isOnline: false,
      lastSeen: '1 day ago',
    },
  ];

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-semibold text-gray-900 dark:text-white flex items-center gap-2">
          <Users className="w-4 h-4" />
          Contacts
        </h3>
        <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
          See All
        </button>
      </div>
      
      <div className="space-y-3">
        {contacts.map((contact) => (
          <div key={contact.id} className="flex items-center justify-between group hover:bg-gray-50 dark:hover:bg-gray-700 p-2 rounded-lg transition-colors">
            <div className="flex items-center gap-3">
              <div className="relative">
                <img
                  src={contact.avatar}
                  alt={contact.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
                {contact.isOnline && (
                  <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full"></div>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {contact.name}
                </p>
                {!contact.isOnline && contact.lastSeen && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    {contact.lastSeen}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <button className="p-1.5 text-gray-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-full transition-colors">
                <MessageCircle className="w-4 h-4" />
              </button>
              <button className="p-1.5 text-gray-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 rounded-full transition-colors">
                <Phone className="w-4 h-4" />
              </button>
              <button className="p-1.5 text-gray-500 hover:text-purple-600 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-full transition-colors">
                <Video className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ContactsWidget;