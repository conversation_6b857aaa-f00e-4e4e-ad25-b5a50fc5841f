import { EventEmitter } from '../utils/EventEmitter';
import {
  Message, Conversation, User, MessageReaction, CallData,
  MessageThread, ChatSettings, StickerPack,
  QuickReply, MessageDraft, ConversationTheme, MessageSearchResult,
  MessageAttachment
} from '@/types/enhanced-messaging';
import { 
  mockUsers, 
  createMockConversations, 
  createMockMessages,
  getUserById,
  getOnlineUsers,
  searchUsers
} from '@/utils/mockMessagingData';

export class EnhancedMessagingService extends EventEmitter {
  private conversations = new Map<string, Conversation>();
  private messages = new Map<string, Message[]>();
  private drafts = new Map<string, MessageDraft>();
  private threads = new Map<string, MessageThread>();
  private currentUserId: string;
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private settings: ChatSettings;
  private customThemes: ConversationTheme[] = [];
  private stickerPacks: StickerPack[] = [];
  private quickReplies: QuickReply[] = [];
  private isConnected = false;

  constructor(userId: string) {
    super();
    this.currentUserId = userId;
    this.settings = {
      theme: 'system',
      fontSize: 'medium',
      enterToSend: true,
      soundEnabled: true,
      notificationPreview: true,
      activeStatus: true,
      readReceipts: true,
      autoDownloadMedia: 'wifi',
      dataUsage: 'medium'
    };
    this.initialize();
  }

  private async initialize() {
    console.log('🔄 Initializing EnhancedMessagingService for user:', this.currentUserId);
    await this.loadSettings();
    await this.loadCustomThemes();
    await this.loadStickerPacks();
    await this.loadQuickReplies();
    await this.loadDrafts();
    await this.loadMockData(); // Load mock users and conversations
    this.connect();
    // Note: 'initialized' event will be emitted after connection is established
  }

  // Load mock data for demonstration
  private async loadMockData() {
    console.log('🔄 Loading mock messaging data...');
    
    // Load mock conversations
    const mockConversations = createMockConversations(this.currentUserId);
    console.log('📥 Created mock conversations:', mockConversations.length);
    
    mockConversations.forEach(conversation => {
      this.conversations.set(conversation.id, conversation);
      
      // Load mock messages for each conversation
      const messages = createMockMessages(conversation.id, this.currentUserId);
      this.messages.set(conversation.id, messages);
    });

    console.log(`✅ Loaded ${mockConversations.length} mock conversations with messages`);
    console.log('📋 Conversations:', mockConversations.map(c => ({ 
      id: c.id, 
      type: c.type, 
      name: c.user?.name || c.name,
      unreadCount: c.unreadCount 
    })));
  }

  // Get all users (including mock users)
  public getUsers(): User[] {
    return [...mockUsers];
  }

  // Get user by ID
  public getUser(userId: string): User | undefined {
    return getUserById(userId);
  }

  // Get online users
  public getOnlineUsers(): User[] {
    return getOnlineUsers();
  }

  // Search users
  public searchUsers(query: string): User[] {
    return searchUsers(query);
  }

  // Get conversations
  public getConversations(): Conversation[] {
    return Array.from(this.conversations.values()).sort((a, b) => {
      // Use createdAt as fallback if updatedAt is not available
      const aTime = a.updatedAt || a.createdAt || new Date();
      const bTime = b.updatedAt || b.createdAt || new Date();
      return new Date(bTime).getTime() - new Date(aTime).getTime();
    });
  }

  // Get messages for a conversation
  public getMessages(conversationId: string): Message[] {
    return this.messages.get(conversationId) || [];
  }

  // Get connection status
  public getConnectionStatus(): { connected: boolean; status: string } {
    return {
      connected: this.isConnected,
      status: this.isConnected ? 'Connected' : 'Disconnected'
    };
  }

  // WebSocket Connection Management (Simulated for Demo)
  private async connect() {
    try {
      console.log('🔄 Simulating WebSocket connection for demo...');
      
      // Add a slight delay to ensure proper event handling
      setTimeout(() => {
        console.log('✅ Enhanced messaging service connected (simulated)');
        this.reconnectAttempts = 0;
        this.isConnected = true;
        this.emit('connected');
        
        // Emit initialized after connection is established
        setTimeout(() => {
          console.log('🚀 Emitting initialized event');
          this.emit('initialized');
        }, 50);
        
      }, 200);

    } catch (error) {
      console.error('Failed to connect to enhanced messaging service:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    setTimeout(() => {
      console.log(`Reconnect attempt ${this.reconnectAttempts}`);
      this.connect();
    }, delay);
  }

  private authenticateConnection() {
    // Simulate successful authentication for demo
    console.log('🔐 Authenticating connection (simulated)...');
    setTimeout(() => {
      console.log('✅ Authentication successful');
      this.emit('authenticated', { userId: this.currentUserId });
    }, 500);
  }

  // Message Handling (Simulated for Demo)
  private handleMessage(data: { type: string; payload: unknown }) {
    switch (data.type) {
      case 'message':
        this.handleIncomingMessage(data.payload);
        break;
      case 'messageStatus':
        this.handleMessageStatusUpdate(data.payload);
        break;
      case 'messageReaction':
        this.handleMessageReaction(data.payload);
        break;
      case 'typing':
        this.handleTypingIndicator(data.payload);
        break;
      case 'userStatus':
        this.handleUserStatusUpdate(data.payload);
        break;
      case 'conversationUpdate':
        this.handleConversationUpdate(data.payload);
        break;
      case 'callSignal':
        this.handleCallSignal(data.payload);
        break;
      case 'threadUpdate':
        this.handleThreadUpdate(data.payload);
        break;
      case 'messageSearch':
        this.handleSearchResults(data.payload);
        break;
      default:
        console.warn('Unknown message type:', data.type);
    }
  }

  // Enhanced Message Operations
  public async sendMessage(
    conversationId: string, 
    content: string, 
    options: {
      type?: Message['type'];
      replyTo?: string;
      attachments?: File[];
      mentions?: string[];
      metadata?: Message['metadata'];
    } = {}
  ): Promise<Message> {
    console.log('📤 Sending message to conversation:', conversationId, 'Content:', content);
    
    const message: Message = {
      id: this.generateMessageId(),
      conversationId,
      senderId: this.currentUserId,
      content,
      timestamp: new Date(),
      type: options.type || 'text',
      status: 'sending',
      replyTo: options.replyTo,
      mentions: options.mentions,
      metadata: options.metadata
    };

    console.log('📨 Created message object:', message);

    // Handle attachments
    if (options.attachments && options.attachments.length > 0) {
      message.attachments = await this.processAttachments(options.attachments);
    }

    // Add to local messages
    const conversationMessages = this.messages.get(conversationId) || [];
    this.messages.set(conversationId, [...conversationMessages, message]);
    console.log('💾 Added message to local storage. Total messages for conversation:', 
                this.messages.get(conversationId)?.length);

    // Emit message immediately for UI update
    this.emit('messageAdded', message);

    // Update conversation
    this.updateConversationLastMessage(conversationId, message);

    // Send message to server (simulated)
    if (this.isConnected) {
      console.log('📤 Sending message to server (simulated):', message);
      // Simulate server acknowledgment
      setTimeout(() => {
        console.log('🔄 Server acknowledged message');
      }, 50);
    }

    // Clear draft
    this.clearDraft(conversationId);

    // Update message status to sent after a brief delay (simulate network)
    setTimeout(() => {
      message.status = 'sent';
      console.log('✅ Message status updated to sent');
      this.emit('messageSent', message);
    }, 100);

    return message;
  }

  public async editMessage(messageId: string, newContent: string): Promise<void> {
    const message = this.findMessage(messageId);
    if (!message || message.senderId !== this.currentUserId) {
      throw new Error('Cannot edit this message');
    }

    message.content = newContent;
    message.isEdited = true;
    message.editedAt = new Date();

    if (this.isConnected) {
      console.log('📝 Sending edit message to server (simulated)');
    }

    this.emit('messageEdited', message);
  }

  public async deleteMessage(messageId: string, deleteForEveryone = false): Promise<void> {
    const message = this.findMessage(messageId);
    if (!message) return;

    if (deleteForEveryone && message.senderId !== this.currentUserId) {
      throw new Error('Cannot delete this message for everyone');
    }

    message.isDeleted = true;
    message.deletedAt = new Date();

    if (this.isConnected) {
      console.log('🗑️ Sending delete message to server (simulated)');
    }

    this.emit('messageDeleted', message);
  }

  public async forwardMessage(messageId: string, conversationIds: string[]): Promise<void> {
    const originalMessage = this.findMessage(messageId);
    if (!originalMessage) return;

    for (const conversationId of conversationIds) {
      const forwardedMessage: Message = {
        ...originalMessage,
        id: this.generateMessageId(),
        conversationId,
        senderId: this.currentUserId,
        timestamp: new Date(),
        status: 'sending',
        isForwarded: true,
        forwardedFrom: originalMessage.senderId,
        replyTo: undefined // Remove reply context when forwarding
      };

      await this.sendMessage(conversationId, forwardedMessage.content, {
        type: forwardedMessage.type,
        attachments: [], // Handle attachment forwarding separately
        metadata: forwardedMessage.metadata
      });
    }

    this.emit('messageForwarded', { originalMessage, conversationIds });
  }

  // Reaction Management
  public async addReaction(messageId: string, emoji: string): Promise<void> {
    const message = this.findMessage(messageId);
    if (!message) return;

    const existingReaction = message.reactions?.find(r => r.userId === this.currentUserId);

    if (existingReaction) {
      if (existingReaction.emoji === emoji) {
        // Remove reaction
        message.reactions = message.reactions?.filter(r => r.userId !== this.currentUserId);
      } else {
        // Update reaction
        existingReaction.emoji = emoji;
        existingReaction.timestamp = new Date();
      }
    } else {
      // Add new reaction
      const reaction: MessageReaction = {
        id: this.generateReactionId(),
        emoji,
        userId: this.currentUserId,
        timestamp: new Date()
      };

      message.reactions = [...(message.reactions || []), reaction];
    }

    if (this.isConnected) {
      console.log('👍 Sending reaction to server (simulated)');
    }

    this.emit('reactionAdded', { messageId, emoji, userId: this.currentUserId });
  }

  // Thread Management
  public async createThread(parentMessageId: string): Promise<MessageThread> {
    const thread: MessageThread = {
      parentMessageId,
      replies: [],
      participantCount: 1,
      lastReplyAt: new Date()
    };

    this.threads.set(parentMessageId, thread);
    this.emit('threadCreated', thread);
    return thread;
  }

  public async replyToThread(parentMessageId: string, content: string): Promise<Message> {
    const thread = this.threads.get(parentMessageId);
    if (!thread) {
      throw new Error('Thread not found');
    }

    const parentMessage = this.findMessage(parentMessageId);
    if (!parentMessage) {
      throw new Error('Parent message not found');
    }

    const reply = await this.sendMessage(parentMessage.conversationId, content, {
      replyTo: parentMessageId
    });

    thread.replies.push(reply);
    thread.lastReplyAt = new Date();
    thread.participantCount = new Set([
      ...thread.replies.map(r => r.senderId),
      parentMessage.senderId
    ]).size;

    this.emit('threadUpdated', thread);
    return reply;
  }

  // Search Functionality
  public async searchMessages(
    query: string,
    filters: {
      conversationId?: string;
      senderId?: string;
      type?: Message['type'];
      dateFrom?: Date;
      dateTo?: Date;
    } = {}
  ): Promise<MessageSearchResult[]> {
    const results: MessageSearchResult[] = [];
    const searchTerms = query.toLowerCase().split(' ');

    for (const [conversationId, messages] of this.messages.entries()) {
      if (filters.conversationId && conversationId !== filters.conversationId) {
        continue;
      }

      messages.forEach((message, index) => {
        // Apply filters
        if (filters.senderId && message.senderId !== filters.senderId) return;
        if (filters.type && message.type !== filters.type) return;
        if (filters.dateFrom && message.timestamp < filters.dateFrom) return;
        if (filters.dateTo && message.timestamp > filters.dateTo) return;

        // Search in content
        const content = message.content.toLowerCase();
        const matches = searchTerms.every(term => content.includes(term));

        if (matches) {
          const before = messages.slice(Math.max(0, index - 2), index);
          const after = messages.slice(index + 1, Math.min(messages.length, index + 3));

          results.push({
            message,
            conversationId,
            context: { before, after },
            highlights: searchTerms
          });
        }
      });
    }

    return results.sort((a, b) => 
      new Date(b.message.timestamp).getTime() - new Date(a.message.timestamp).getTime()
    );
  }

  // Draft Management
  public saveDraft(conversationId: string, content: string, replyTo?: string): void {
    const draft: MessageDraft = {
      conversationId,
      content,
      replyTo,
      timestamp: new Date()
    };

    this.drafts.set(conversationId, draft);
    localStorage.setItem(`draft_${conversationId}`, JSON.stringify(draft));
  }

  public getDraft(conversationId: string): MessageDraft | null {
    return this.drafts.get(conversationId) || null;
  }

  public clearDraft(conversationId: string): void {
    this.drafts.delete(conversationId);
    localStorage.removeItem(`draft_${conversationId}`);
  }

  private async loadDrafts(): Promise<void> {
    // Load drafts from localStorage
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key?.startsWith('draft_')) {
        const conversationId = key.replace('draft_', '');
        const draftData = localStorage.getItem(key);
        if (draftData) {
          try {
            const draft = JSON.parse(draftData);
            this.drafts.set(conversationId, draft);
          } catch (error) {
            console.error('Failed to parse draft:', error);
          }
        }
      }
    }
  }

  // Call Management
  public async startCall(conversationId: string, type: 'audio' | 'video'): Promise<CallData> {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    const call: CallData = {
      id: this.generateCallId(),
      conversationId,
      type,
      participants: conversation.type === 'direct' 
        ? [this.currentUserId, conversation.user!.id]
        : [this.currentUserId, ...(conversation.participants?.map(p => p.id) || [])],
      status: 'ringing',
      initiatorId: this.currentUserId,
      startedAt: new Date()
    };

    if (this.isConnected) {
      console.log('📞 Sending start call to server (simulated)');
    }

    this.emit('callStarted', call);
    return call;
  }

  public async endCall(callId: string): Promise<void> {
    if (this.isConnected) {
      console.log('📞 Sending end call to server (simulated)');
    }

    this.emit('callEnded', callId);
  }

  // Settings Management
  public updateSettings(newSettings: Partial<ChatSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    localStorage.setItem('messagingSettings', JSON.stringify(this.settings));
    this.emit('settingsUpdated', this.settings);
  }

  public getSettings(): ChatSettings {
    return { ...this.settings };
  }

  private async loadSettings(): Promise<void> {
    const savedSettings = localStorage.getItem('messagingSettings');
    if (savedSettings) {
      try {
        this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    }
  }

  // Theme Management
  public async loadCustomThemes(): Promise<void> {
    // Load from API or localStorage
    const themes = localStorage.getItem('customThemes');
    if (themes) {
      try {
        this.customThemes = JSON.parse(themes);
      } catch (error) {
        console.error('Failed to load custom themes:', error);
      }
    }
  }

  public getCustomThemes(): ConversationTheme[] {
    return [...this.customThemes];
  }

  // Sticker Management
  private async loadStickerPacks(): Promise<void> {
    // Mock sticker packs - in real app, load from API
    this.stickerPacks = [
      {
        id: 'pack_1',
        name: 'Reactions',
        description: 'Express your emotions',
        thumbnail: '😄',
        stickers: [
          { id: 'sticker_1', packId: 'pack_1', url: '😄', name: 'Happy' },
          { id: 'sticker_2', packId: 'pack_1', url: '😢', name: 'Sad' },
          { id: 'sticker_3', packId: 'pack_1', url: '😍', name: 'Love' },
          { id: 'sticker_4', packId: 'pack_1', url: '😂', name: 'Laugh' },
          { id: 'sticker_5', packId: 'pack_1', url: '😮', name: 'Surprised' },
          { id: 'sticker_6', packId: 'pack_1', url: '😡', name: 'Angry' }
        ],
        isOwned: true
      }
    ];
  }

  public getStickerPacks(): StickerPack[] {
    return [...this.stickerPacks];
  }

  // Quick Replies
  private async loadQuickReplies(): Promise<void> {
    const saved = localStorage.getItem('quickReplies');
    if (saved) {
      try {
        this.quickReplies = JSON.parse(saved);
      } catch (error) {
        console.error('Failed to load quick replies:', error);
      }
    } else {
      // Default quick replies
      this.quickReplies = [
        { id: 'qr_1', text: 'Thanks!', category: 'gratitude' },
        { id: 'qr_2', text: 'Sounds good', category: 'agreement' },
        { id: 'qr_3', text: 'On my way', category: 'status' },
        { id: 'qr_4', text: 'Can we talk later?', category: 'scheduling' },
        { id: 'qr_5', text: 'No problem', category: 'reassurance' }
      ];
    }
  }

  public getQuickReplies(): QuickReply[] {
    return [...this.quickReplies];
  }

  public addQuickReply(text: string, category?: string): void {
    const quickReply: QuickReply = {
      id: `qr_${Date.now()}`,
      text,
      category,
      usageCount: 0
    };

    this.quickReplies.push(quickReply);
    localStorage.setItem('quickReplies', JSON.stringify(this.quickReplies));
    this.emit('quickReplyAdded', quickReply);
  }

  // Utility Methods
  private async processAttachments(files: File[]): Promise<MessageAttachment[]> {
    // In a real implementation, this would upload files and return attachment data
    return files.map(file => ({
      id: this.generateAttachmentId(),
      type: file.type.startsWith('image/') ? 'image' : 'file',
      url: URL.createObjectURL(file),
      name: file.name,
      size: file.size,
      mimeType: file.type
    }));
  }

  private findMessage(messageId: string): Message | null {
    for (const messages of this.messages.values()) {
      const message = messages.find(m => m.id === messageId);
      if (message) return message;
    }
    return null;
  }

  private updateConversationLastMessage(conversationId: string, message: Message): void {
    const conversation = this.conversations.get(conversationId);
    if (conversation) {
      conversation.lastMessage = {
        content: message.content,
        timestamp: message.timestamp.toISOString(),
        isRead: message.senderId === this.currentUserId,
        senderId: message.senderId
      };
      conversation.updatedAt = message.timestamp;
      console.log('🔄 Updated conversation last message:', conversationId, 'New timestamp:', message.timestamp);
      console.log('📋 Conversation will move to top due to updated timestamp');
      this.emit('conversationUpdated', conversation);
    }
  }

  // Event Handlers
  private handleIncomingMessage(message: Message): void {
    const conversationMessages = this.messages.get(message.conversationId) || [];
    this.messages.set(message.conversationId, [...conversationMessages, message]);
    this.updateConversationLastMessage(message.conversationId, message);
    this.emit('messageReceived', message);
  }

  private handleMessageStatusUpdate(data: { messageId: string; status: Message['status'] }): void {
    const message = this.findMessage(data.messageId);
    if (message) {
      message.status = data.status;
      this.emit('messageStatusUpdated', message);
    }
  }

  private handleMessageReaction(data: { messageId: string; emoji: string; userId: string }): void {
    this.emit('messageReactionReceived', data);
  }

  private handleTypingIndicator(data: { conversationId: string; userId: string; isTyping: boolean }): void {
    this.emit('typingIndicator', data);
  }

  private handleUserStatusUpdate(data: { userId: string; isOnline: boolean; lastActive: string }): void {
    this.emit('userStatusUpdated', data);
  }

  private handleConversationUpdate(conversation: Conversation): void {
    this.conversations.set(conversation.id, conversation);
    this.emit('conversationUpdated', conversation);
  }

  private handleCallSignal(data: unknown): void {
    this.emit('callSignal', data);
  }

  private handleThreadUpdate(thread: MessageThread): void {
    this.threads.set(thread.parentMessageId, thread);
    this.emit('threadUpdated', thread);
  }

  private handleSearchResults(results: MessageSearchResult[]): void {
    this.emit('searchResults', results);
  }

  // ID Generators
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateReactionId(): string {
    return `reaction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCallId(): string {
    return `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAttachmentId(): string {
    return `att_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Cleanup
  public disconnect(): void {
    this.isConnected = false;
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.emit('disconnected');
    this.removeAllListeners();
  }
}

// Singleton instance per user
const enhancedMessagingServices = new Map<string, EnhancedMessagingService>();

export const getEnhancedMessagingService = (userId?: string): EnhancedMessagingService => {
  if (!userId) {
    throw new Error('User ID is required to get enhanced messaging service');
  }
  
  if (!enhancedMessagingServices.has(userId)) {
    enhancedMessagingServices.set(userId, new EnhancedMessagingService(userId));
  }
  
  return enhancedMessagingServices.get(userId)!;
};
