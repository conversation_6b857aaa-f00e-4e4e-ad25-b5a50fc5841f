import { useState, useEffect, useCallback } from 'react';
import { storage } from '@/lib/storage';
import { STORAGE_KEYS, MOCK_IMAGES } from '@/lib/constants';
import { toast } from 'sonner';

export interface SavedItem {
  id: string;
  originalId?: string;
  type: 'post' | 'video' | 'article' | 'event' | 'marketplace' | 'photo' | 'link' | 'group';
  title: string;
  content?: string;
  image?: string;
  savedDate: string;
  collection: string;
  url?: string;
  isFavorite?: boolean;
  description?: string;
  creator?: {
    name: string;
    avatar: string;
    verified?: boolean;
  };
  engagement?: {
    likes: number;
    comments: number;
    shares: number;
  };
  originalDate?: string;
}

// Custom hook for managing saved items
export const useSavedItems = () => {
  const [items, setItems] = useState<SavedItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load saved items from storage
  const loadSavedItems = useCallback(() => {
    setIsLoading(true);
    
    try {
      // Get saved items from storage
      const savedItems = storage.get<SavedItem[]>(STORAGE_KEYS.SAVED_ITEMS, []);
      const savedPosts = storage.get<string[]>(STORAGE_KEYS.SAVED_POSTS, []);
      
      let allItems: SavedItem[] = [];
      
      if (savedItems && savedItems.length > 0) {
        allItems = [...savedItems];
      }
      
      // Add saved posts if they're not already in the items
      if (savedPosts && savedPosts.length > 0) {
        const existingIds = new Set(allItems.map(item => item.id));
        
        // Create saved item entries for posts
        const postItems: SavedItem[] = savedPosts
          .filter(id => !existingIds.has(id))
          .map(id => ({
            id,
            type: 'post' as const,
            title: `Saved Post #${id}`,
            content: 'This is a saved post from your news feed.',
            image: MOCK_IMAGES.POSTS[parseInt(id) % MOCK_IMAGES.POSTS.length],
            savedDate: new Date().toISOString(),
            collection: 'Posts',
            creator: {
              name: ['Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'David Kim'][parseInt(id) % 4],
              avatar: MOCK_IMAGES.AVATARS[parseInt(id) % MOCK_IMAGES.AVATARS.length],
              verified: Math.random() > 0.5
            },
            engagement: {
              likes: Math.floor(Math.random() * 500),
              comments: Math.floor(Math.random() * 100),
              shares: Math.floor(Math.random() * 50)
            },
            originalDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
          }));
        
        allItems = [...allItems, ...postItems];
      }
      
      // Save all items to storage to keep them in sync
      if (allItems.length > 0) {
        storage.set(STORAGE_KEYS.SAVED_ITEMS, allItems);
      }
      
      setItems(allItems);
    } catch (error) {
      console.error('Error loading saved items:', error);
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save a new post item
  const savePostItem = useCallback((postId: string, postData?: Partial<SavedItem>) => {
    const newItem: SavedItem = {
      id: postId,
      type: 'post',
      title: postData?.title || `Saved Post #${postId}`,
      content: postData?.content || 'This is a saved post from your news feed.',
      image: postData?.image || undefined, // Don't use fallback mock images
      savedDate: new Date().toISOString(),
      collection: 'Posts',
      creator: postData?.creator || {
        name: ['Sarah Johnson', 'Mike Chen', 'Emma Wilson', 'David Kim'][parseInt(postId) % 4],
        avatar: MOCK_IMAGES.AVATARS[parseInt(postId) % MOCK_IMAGES.AVATARS.length],
        verified: Math.random() > 0.5
      },
      engagement: postData?.engagement || {
        likes: Math.floor(Math.random() * 500),
        comments: Math.floor(Math.random() * 100),
        shares: Math.floor(Math.random() * 50)
      },
      originalDate: postData?.originalDate || new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      ...postData
    };

    // Update items state
    setItems(prevItems => {
      const existingIndex = prevItems.findIndex(item => item.id === postId);
      if (existingIndex !== -1) {
        return prevItems; // Already exists
      }
      return [newItem, ...prevItems];
    });

    // Update storage
    const currentItems = storage.get<SavedItem[]>(STORAGE_KEYS.SAVED_ITEMS, []);
    const existingIndex = currentItems.findIndex(item => item.id === postId);
    if (existingIndex === -1) {
      storage.set(STORAGE_KEYS.SAVED_ITEMS, [newItem, ...currentItems]);
    }

    // Also update saved posts
    const savedPosts = storage.get<string[]>(STORAGE_KEYS.SAVED_POSTS, []);
    if (!savedPosts.includes(postId)) {
      savedPosts.push(postId);
      storage.set(STORAGE_KEYS.SAVED_POSTS, savedPosts);
    }

    // Trigger storage event for cross-component sync
    window.dispatchEvent(new StorageEvent('storage', {
      key: STORAGE_KEYS.SAVED_ITEMS,
      newValue: JSON.stringify([newItem, ...currentItems])
    }));
  }, []);

  // Remove a saved item
  const removeItem = useCallback((itemId: string) => {
    // Update items state
    setItems(prevItems => prevItems.filter(item => item.id !== itemId));

    // Update storage
    const currentItems = storage.get<SavedItem[]>(STORAGE_KEYS.SAVED_ITEMS, []);
    const updatedItems = currentItems.filter(item => item.id !== itemId);
    storage.set(STORAGE_KEYS.SAVED_ITEMS, updatedItems);

    // Also remove from saved posts if it's a post
    const savedPosts = storage.get<string[]>(STORAGE_KEYS.SAVED_POSTS, []);
    if (savedPosts.includes(itemId)) {
      const updatedPosts = savedPosts.filter(id => id !== itemId);
      storage.set(STORAGE_KEYS.SAVED_POSTS, updatedPosts);
    }

    // Trigger storage event for cross-component sync
    window.dispatchEvent(new StorageEvent('storage', {
      key: STORAGE_KEYS.SAVED_ITEMS,
      newValue: JSON.stringify(updatedItems)
    }));

    toast.success('Item removed from saved');
  }, []);

  // Check if an item is saved
  const isItemSaved = useCallback((itemId: string): boolean => {
    return items.some(item => item.id === itemId);
  }, [items]);

  // Load items on mount
  useEffect(() => {
    loadSavedItems();
  }, [loadSavedItems]);

  // Listen for storage events to sync across components
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === STORAGE_KEYS.SAVED_ITEMS || e.key === STORAGE_KEYS.SAVED_POSTS) {
        loadSavedItems();
      }
    };

    // Listen for both storage events and custom events
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [loadSavedItems]);

  return {
    items,
    isLoading,
    savePostItem,
    removeItem,
    isItemSaved,
    loadSavedItems
  };
};
