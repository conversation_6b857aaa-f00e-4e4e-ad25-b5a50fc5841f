import React, { useState, use<PERSON>allback, memo, useMemo } from 'react';
import { Calendar, MapPin, Users, Heart, Plus, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from 'sonner';
import { format, isAfter, isBefore, startOfDay, endOfDay } from 'date-fns';

interface EventAttendee {
  id: string;
  name: string;
  avatar: string;
  status: 'going' | 'interested' | 'not_going';
  joinedAt: Date;
}

interface Event {
  id: string;
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  isVirtual: boolean;
  virtualLink?: string;
  organizer: {
    id: string;
    name: string;
    avatar: string;
  };
  attendees: EventAttendee[];
  coverImage?: string;
  category: string;
  tags: string[];
  privacy: 'public' | 'private' | 'friends';
  maxAttendees?: number;
  ticketPrice?: number;
  currency?: string;
  isRecurring: boolean;
  recurringPattern?: string;
  createdAt: Date;
}

interface EnhancedEventManagerProps {
  currentUserId: string;
  onEventCreate?: (event: Omit<Event, 'id' | 'createdAt'>) => void;
  onEventUpdate?: (eventId: string, updates: Partial<Event>) => void;
  onEventDelete?: (eventId: string) => void;
}

const EnhancedEventManager: React.FC<EnhancedEventManagerProps> = memo(({
  currentUserId,
  onEventCreate,
  _onEventUpdate,
  _onEventDelete
}) => {
  const [events, setEvents] = useState<Event[]>([]);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedTimeFilter, setSelectedTimeFilter] = useState<string>('upcoming');

  // New event form state
  const [newEvent, setNewEvent] = useState({
    title: '',
    description: '',
    startDate: '',
    endDate: '',
    location: '',
    isVirtual: false,
    virtualLink: '',
    category: 'social',
    tags: [] as string[],
    privacy: 'public' as 'public' | 'private' | 'friends',
    maxAttendees: undefined as number | undefined,
    ticketPrice: undefined as number | undefined,
    isRecurring: false,
    recurringPattern: ''
  });

  // Mock events data
  React.useEffect(() => {
    const mockEvents: Event[] = [
      {
        id: '1',
        title: 'Tech Meetup: React Best Practices',
        description: 'Join us for an evening of learning about React best practices and networking with fellow developers.',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000),
        location: 'Tech Hub Downtown',
        isVirtual: false,
        organizer: {
          id: '2',
          name: 'Sarah Johnson',
          avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=100&h=100&fit=crop&crop=face'
        },
        attendees: [
          {
            id: currentUserId,
            name: 'You',
            avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
            status: 'going',
            joinedAt: new Date()
          }
        ],
        coverImage: 'https://images.pexels.com/photos/1181676/pexels-photo-1181676.jpeg?w=800&h=400&fit=crop',
        category: 'technology',
        tags: ['react', 'javascript', 'networking'],
        privacy: 'public',
        maxAttendees: 50,
        isRecurring: false,
        createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      },
      {
        id: '2',
        title: 'Virtual Photography Workshop',
        description: 'Learn advanced photography techniques from professional photographers in this interactive online workshop.',
        startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
        isVirtual: true,
        virtualLink: 'https://zoom.us/j/123456789',
        organizer: {
          id: '3',
          name: 'Mike Chen',
          avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?w=100&h=100&fit=crop&crop=face'
        },
        attendees: [],
        coverImage: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=800&h=400&fit=crop',
        category: 'education',
        tags: ['photography', 'workshop', 'online'],
        privacy: 'public',
        ticketPrice: 25,
        currency: 'USD',
        isRecurring: false,
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      }
    ];
    setEvents(mockEvents);
  }, [currentUserId]);

  const categories = useMemo(() => [
    'all', 'social', 'technology', 'education', 'business', 'health', 'sports', 'arts', 'music', 'food'
  ], []);

  const filteredEvents = useMemo(() => {
    return events.filter(event => {
      // Search filter
      if (searchQuery && !event.title.toLowerCase().includes(searchQuery.toLowerCase()) &&
          !event.description.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }

      // Category filter
      if (selectedCategory !== 'all' && event.category !== selectedCategory) {
        return false;
      }

      // Time filter
      const now = new Date();
      switch (selectedTimeFilter) {
        case 'today':
          return isAfter(event.startDate, startOfDay(now)) && isBefore(event.startDate, endOfDay(now));
        case 'upcoming':
          return isAfter(event.startDate, now);
        case 'past':
          return isBefore(event.endDate, now);
        default:
          return true;
      }
    });
  }, [events, searchQuery, selectedCategory, selectedTimeFilter]);

  const handleCreateEvent = useCallback(() => {
    if (!newEvent.title.trim() || !newEvent.startDate || !newEvent.endDate) {
      toast.error('Please fill in all required fields');
      return;
    }

    const event: Omit<Event, 'id' | 'createdAt'> = {
      title: newEvent.title,
      description: newEvent.description,
      startDate: new Date(newEvent.startDate),
      endDate: new Date(newEvent.endDate),
      location: newEvent.location,
      isVirtual: newEvent.isVirtual,
      virtualLink: newEvent.virtualLink,
      organizer: {
        id: currentUserId,
        name: 'You',
        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face'
      },
      attendees: [{
        id: currentUserId,
        name: 'You',
        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
        status: 'going',
        joinedAt: new Date()
      }],
      category: newEvent.category,
      tags: newEvent.tags,
      privacy: newEvent.privacy,
      maxAttendees: newEvent.maxAttendees,
      ticketPrice: newEvent.ticketPrice,
      currency: newEvent.ticketPrice ? 'USD' : undefined,
      isRecurring: newEvent.isRecurring,
      recurringPattern: newEvent.recurringPattern
    };

    const newEventWithId: Event = {
      ...event,
      id: Date.now().toString(),
      createdAt: new Date()
    };

    setEvents(prev => [newEventWithId, ...prev]);
    onEventCreate?.(event);
    setShowCreateDialog(false);
    setNewEvent({
      title: '',
      description: '',
      startDate: '',
      endDate: '',
      location: '',
      isVirtual: false,
      virtualLink: '',
      category: 'social',
      tags: [],
      privacy: 'public',
      maxAttendees: undefined,
      ticketPrice: undefined,
      isRecurring: false,
      recurringPattern: ''
    });
    toast.success('Event created successfully!');
  }, [newEvent, currentUserId, onEventCreate]);

  const handleAttendanceChange = useCallback((eventId: string, status: 'going' | 'interested' | 'not_going') => {
    setEvents(prev => prev.map(event => {
      if (event.id === eventId) {
        const existingAttendee = event.attendees.find(a => a.id === currentUserId);
        let newAttendees = [...event.attendees];

        if (existingAttendee) {
          if (status === 'not_going') {
            newAttendees = newAttendees.filter(a => a.id !== currentUserId);
          } else {
            newAttendees = newAttendees.map(a => 
              a.id === currentUserId ? { ...a, status } : a
            );
          }
        } else if (status !== 'not_going') {
          newAttendees.push({
            id: currentUserId,
            name: 'You',
            avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?w=100&h=100&fit=crop&crop=face',
            status,
            joinedAt: new Date()
          });
        }

        return { ...event, attendees: newAttendees };
      }
      return event;
    }));

    const statusMessages = {
      going: 'You\'re going to this event!',
      interested: 'Marked as interested',
      not_going: 'Removed from event'
    };
    toast.success(statusMessages[status]);
  }, [currentUserId]);

  const getUserAttendanceStatus = useCallback((event: Event) => {
    const attendee = event.attendees.find(a => a.id === currentUserId);
    return attendee?.status || 'not_going';
  }, [currentUserId]);

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Events</h1>
          <p className="text-gray-600 dark:text-gray-300">Discover and create amazing events</p>
        </div>
        <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-500 hover:bg-blue-600">
          <Plus className="w-4 h-4 mr-2" />
          Create Event
        </Button>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search events..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedTimeFilter} onValueChange={setSelectedTimeFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Time</SelectItem>
            <SelectItem value="today">Today</SelectItem>
            <SelectItem value="upcoming">Upcoming</SelectItem>
            <SelectItem value="past">Past</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Events Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredEvents.map((event) => {
            const userStatus = getUserAttendanceStatus(event);
            const goingCount = event.attendees.filter(a => a.status === 'going').length;
            const interestedCount = event.attendees.filter(a => a.status === 'interested').length;

            return (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="group"
              >
                <Card className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <div 
                    className="relative h-48 bg-cover bg-center"
                    style={{ backgroundImage: `url(${event.coverImage})` }}
                    onClick={() => {
                      setSelectedEvent(event);
                      setShowEventDetails(true);
                    }}
                  >
                    <div className="absolute inset-0 bg-black bg-opacity-40" />
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-white/90 text-gray-900">
                        {event.category}
                      </Badge>
                    </div>
                    <div className="absolute top-3 right-3">
                      {event.isVirtual && (
                        <Badge variant="secondary" className="bg-blue-500 text-white">
                          Virtual
                        </Badge>
                      )}
                    </div>
                    <div className="absolute bottom-3 left-3 text-white">
                      <p className="text-sm font-medium">
                        {format(event.startDate, 'MMM dd, yyyy')}
                      </p>
                      <p className="text-xs opacity-90">
                        {format(event.startDate, 'h:mm a')} - {format(event.endDate, 'h:mm a')}
                      </p>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                      {event.title}
                    </h3>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                      {event.description}
                    </p>
                    
                    <div className="flex items-center space-x-2 mb-3">
                      <Avatar className="w-6 h-6">
                        <AvatarImage src={event.organizer.avatar} />
                        <AvatarFallback className="text-xs">
                          {event.organizer.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-gray-600 dark:text-gray-300">
                        by {event.organizer.name}
                      </span>
                    </div>

                    {event.location && (
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <MapPin className="w-4 h-4 mr-1" />
                        {event.location}
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 text-sm text-gray-500 dark:text-gray-400">
                        <span className="flex items-center">
                          <Users className="w-4 h-4 mr-1" />
                          {goingCount} going
                        </span>
                        {interestedCount > 0 && (
                          <span className="flex items-center">
                            <Heart className="w-4 h-4 mr-1" />
                            {interestedCount} interested
                          </span>
                        )}
                      </div>
                      
                      <div className="flex space-x-1">
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAttendanceChange(event.id, userStatus === 'going' ? 'not_going' : 'going');
                          }}
                          variant={userStatus === 'going' ? 'default' : 'outline'}
                          size="sm"
                          className="text-xs"
                        >
                          {userStatus === 'going' ? 'Going' : 'Join'}
                        </Button>
                        
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAttendanceChange(event.id, userStatus === 'interested' ? 'not_going' : 'interested');
                          }}
                          variant={userStatus === 'interested' ? 'default' : 'outline'}
                          size="sm"
                          className="text-xs"
                        >
                          <Heart className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {filteredEvents.length === 0 && (
        <div className="text-center py-12">
          <Calendar className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No events found
          </h3>
          <p className="text-gray-500 dark:text-gray-400 mb-4">
            {searchQuery ? 'Try adjusting your search terms' : 'Create your first event to get started'}
          </p>
          {!searchQuery && (
            <Button onClick={() => setShowCreateDialog(true)} className="bg-blue-500 hover:bg-blue-600">
              <Plus className="w-4 h-4 mr-2" />
              Create Event
            </Button>
          )}
        </div>
      )}

      {/* Create Event Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Event</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="eventTitle">Event Title *</Label>
              <Input
                id="eventTitle"
                placeholder="Enter event title"
                value={newEvent.title}
                onChange={(e) => setNewEvent(prev => ({ ...prev, title: e.target.value }))}
              />
            </div>
            
            <div>
              <Label htmlFor="eventDescription">Description</Label>
              <Textarea
                id="eventDescription"
                placeholder="Describe your event"
                value={newEvent.description}
                onChange={(e) => setNewEvent(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startDate">Start Date & Time *</Label>
                <Input
                  id="startDate"
                  type="datetime-local"
                  value={newEvent.startDate}
                  onChange={(e) => setNewEvent(prev => ({ ...prev, startDate: e.target.value }))}
                />
              </div>
              
              <div>
                <Label htmlFor="endDate">End Date & Time *</Label>
                <Input
                  id="endDate"
                  type="datetime-local"
                  value={newEvent.endDate}
                  onChange={(e) => setNewEvent(prev => ({ ...prev, endDate: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="isVirtual"
                checked={newEvent.isVirtual}
                onChange={(e) => setNewEvent(prev => ({ ...prev, isVirtual: e.target.checked }))}
              />
              <Label htmlFor="isVirtual">This is a virtual event</Label>
            </div>

            {newEvent.isVirtual ? (
              <div>
                <Label htmlFor="virtualLink">Virtual Event Link</Label>
                <Input
                  id="virtualLink"
                  placeholder="https://zoom.us/j/..."
                  value={newEvent.virtualLink}
                  onChange={(e) => setNewEvent(prev => ({ ...prev, virtualLink: e.target.value }))}
                />
              </div>
            ) : (
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  placeholder="Enter event location"
                  value={newEvent.location}
                  onChange={(e) => setNewEvent(prev => ({ ...prev, location: e.target.value }))}
                />
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="category">Category</Label>
                <Select 
                  value={newEvent.category} 
                  onValueChange={(value) => setNewEvent(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.filter(c => c !== 'all').map(category => (
                      <SelectItem key={category} value={category}>
                        {category.charAt(0).toUpperCase() + category.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="privacy">Privacy</Label>
                <Select 
                  value={newEvent.privacy} 
                  onValueChange={(value: 'public' | 'private' | 'friends') => 
                    setNewEvent(prev => ({ ...prev, privacy: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="public">Public</SelectItem>
                    <SelectItem value="friends">Friends Only</SelectItem>
                    <SelectItem value="private">Private</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowCreateDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateEvent}
                disabled={!newEvent.title.trim() || !newEvent.startDate || !newEvent.endDate}
                className="bg-blue-500 hover:bg-blue-600"
              >
                Create Event
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});

EnhancedEventManager.displayName = 'EnhancedEventManager';

export default EnhancedEventManager;
