import { useState, useEffect } from 'react';
import { storage } from '@/lib/storage';

interface VideoPreferences {
  autoplayEnabled: boolean;
  lastUserAction: 'play' | 'pause' | null;
  lastActionTimestamp: number;
  respectPauseState: boolean;
}

const DEFAULT_PREFERENCES: VideoPreferences = {
  autoplayEnabled: false,
  lastUserAction: null,
  lastActionTimestamp: 0,
  respectPauseState: true,
};

const STORAGE_KEY = 'userVideoPreferences';

export const useVideoPreferences = () => {
  const [preferences, setPreferences] = useState<VideoPreferences>(DEFAULT_PREFERENCES);

  // Load preferences from storage on mount
  useEffect(() => {
    const stored = storage.get<VideoPreferences>(STORAGE_KEY, DEFAULT_PREFERENCES);
    setPreferences(stored);
  }, []);

  // Save preferences to storage whenever they change
  const updatePreferences = (updates: Partial<VideoPreferences>) => {
    const newPreferences = {
      ...preferences,
      ...updates,
    };
    setPreferences(newPreferences);
    storage.set(STORAGE_KEY, newPreferences);
  };

  // Record user action (play/pause)
  const recordUserAction = (action: 'play' | 'pause') => {
    updatePreferences({
      lastUserAction: action,
      lastActionTimestamp: Date.now(),
    });
  };

  // Check if we should autoplay based on user preferences and recent actions
  const shouldAutoplay = (_videoId?: string): boolean => {
    // Never autoplay if disabled in preferences
    if (!preferences.autoplayEnabled) {
      return false;
    }

    // If user recently paused (within last 10 minutes), don't autoplay
    if (preferences.respectPauseState && preferences.lastUserAction === 'pause') {
      const timeSinceLastAction = Date.now() - preferences.lastActionTimestamp;
      const TEN_MINUTES = 10 * 60 * 1000;
      
      if (timeSinceLastAction < TEN_MINUTES) {
        return false;
      }
    }

    return preferences.autoplayEnabled;
  };

  // Reset user action state (call when user explicitly chooses to play a new video)
  const resetUserActionState = () => {
    updatePreferences({
      lastUserAction: null,
      lastActionTimestamp: 0,
    });
  };

  // Toggle autoplay preference
  const toggleAutoplay = () => {
    updatePreferences({
      autoplayEnabled: !preferences.autoplayEnabled,
    });
  };

  return {
    preferences,
    updatePreferences,
    recordUserAction,
    shouldAutoplay,
    resetUserActionState,
    toggleAutoplay,
  };
};
