<!DOCTYPE html>
<html>
<head>
    <title>Image Debug Test</title>
</head>
<body>
    <h1>Testing News Feed Images</h1>
    
    <h2>Pexels Images (Used in news feed):</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
        <div>
            <p>Post Image 1:</p>
            <img src="https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop" 
                 style="width: 100%; height: 150px; object-fit: cover; border: 1px solid #ccc;" 
                 onload="console.log('Image 1 loaded successfully')" 
                 onerror="console.log('Image 1 failed to load')">
        </div>
        
        <div>
            <p>Post Image 2:</p>
            <img src="https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?w=800&h=600&fit=crop" 
                 style="width: 100%; height: 150px; object-fit: cover; border: 1px solid #ccc;"
                 onload="console.log('Image 2 loaded successfully')" 
                 onerror="console.log('Image 2 failed to load')">
        </div>
        
        <div>
            <p>Avatar Image:</p>
            <img src="https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face" 
                 style="width: 100%; height: 150px; object-fit: cover; border: 1px solid #ccc;"
                 onload="console.log('Avatar image loaded successfully')" 
                 onerror="console.log('Avatar image failed to load')">
        </div>
    </div>
    
    <script>
        console.log('Image debug page loaded');
        // Test network connectivity
        fetch('https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=800&h=600&fit=crop')
            .then(response => {
                console.log('Network test - Response status:', response.status);
                if (response.ok) {
                    console.log('✅ Network connectivity to Pexels is working');
                } else {
                    console.log('❌ Network issue - Status:', response.status);
                }
            })
            .catch(error => {
                console.log('❌ Network error:', error);
            });
    </script>
</body>
</html>
