import React, { useState, useEffect } from 'react';
import PostCard from '@/components/posts/PostCard';
import Stories from '@/components/Stories';
import CreatePost from '@/components/posts/CreatePostNew';
import { useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EnhancedCheckIn, { CheckInData } from '@/components/EnhancedCheckIn';

const Home = () => {
  const location = useLocation();
  const [posts] = useState([
    { id: 1, content: "Welcome to the optimized Facebook clone!", author: "<PERSON>", timestamp: new Date().toISOString() },
    { id: 2, content: "This is a sample post to test the interface.", author: "<PERSON>", timestamp: new Date().toISOString() }
  ]);
  const [isCheckInOpen, setIsCheckInOpen] = useState(false);

  // Handle navigation from notifications
  useEffect(() => {
    if (location.state?.fromNotification) {
      const { highlightPost, notificationContent, notificationType, userInfo } = location.state;
      
      if (highlightPost) {
        toast.success(`📍 Showing post: ${highlightPost}`);
        console.log('🎯 Highlighting post:', highlightPost);
        // Here you could scroll to and highlight the specific post
      } else if (notificationType && userInfo) {
        toast.success(`📱 Opened from ${notificationType} notification by ${userInfo.name}`);
        console.log('🔔 From notification:', { notificationType, userInfo });
      } else {
        toast.success('📱 Opened from notification');
      }
      
      // Clear the state to prevent repeated toasts
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location]);

  const handleCheckIn = (checkInData: CheckInData) => {
    // Handle the check-in data - could save to local storage, send to API, etc.
    console.log('Check-in data:', checkInData);
    toast.success(`✅ Checked in at ${checkInData.location.name}!`);

    // You could also create a post about the check-in
    if (checkInData.message) {
      console.log('Check-in message:', checkInData.message);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-4">
      {/* Stories */}
      <Stories />
      
      {/* Create Post */}
      <CreatePost />
      
      {/* Posts */}
      <div className="space-y-4">
        {posts.map((post) => (
          <PostCard
            key={post.id}
            post={post}
          />
        ))}
      </div>

      {/* Floating Check-in Button */}
      <Button
        onClick={() => setIsCheckInOpen(true)}
        className="fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full bg-emerald-600 hover:bg-emerald-700 shadow-lg hover:shadow-xl transition-all duration-200 md:hidden"
        size="icon"
      >
        <MapPin className="w-6 h-6" />
      </Button>

      {/* Enhanced Check-in Modal */}
      <EnhancedCheckIn
        isOpen={isCheckInOpen}
        onClose={() => setIsCheckInOpen(false)}
        onCheckIn={handleCheckIn}
      />
    </div>
  );
};

export default Home;