import React, { useState, useEffect } from 'react';
import PostCard from '@/components/posts/PostCard';
import Stories from '@/components/Stories';
import CreatePost from '@/components/posts/CreatePostNew';
import { useLocation } from 'react-router-dom';
import { toast } from 'sonner';

const Home = () => {
  const location = useLocation();
  const [posts] = useState([
    { id: 1, content: "Welcome to the optimized Facebook clone!", author: "<PERSON> Do<PERSON>", timestamp: new Date().toISOString() },
    { id: 2, content: "This is a sample post to test the interface.", author: "<PERSON>", timestamp: new Date().toISOString() }
  ]);

  // Handle navigation from notifications
  useEffect(() => {
    if (location.state?.fromNotification) {
      const { highlightPost, notificationContent, notificationType, userInfo } = location.state;
      
      if (highlightPost) {
        toast.success(`📍 Showing post: ${highlightPost}`);
        console.log('🎯 Highlighting post:', highlightPost);
        // Here you could scroll to and highlight the specific post
      } else if (notificationType && userInfo) {
        toast.success(`📱 Opened from ${notificationType} notification by ${userInfo.name}`);
        console.log('🔔 From notification:', { notificationType, userInfo });
      } else {
        toast.success('📱 Opened from notification');
      }
      
      // Clear the state to prevent repeated toasts
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location]);

  return (
    <div className="max-w-2xl mx-auto space-y-4">
      {/* Stories */}
      <Stories />
      
      {/* Create Post */}
      <CreatePost />
      
      {/* Posts */}
      <div className="space-y-4">
        {posts.map((post) => (
          <PostCard 
            key={post.id} 
            post={post}
          />
        ))}
      </div>
    </div>
  );
};

export default Home;