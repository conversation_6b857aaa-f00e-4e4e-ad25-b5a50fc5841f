import React, { useState, useEffect } from 'react';
import PostCard from '@/components/posts/PostCard';
import Stories from '@/components/Stories';
import CreatePost from '@/components/posts/CreatePostNew';
import { useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import EnhancedCheckIn, { CheckInData } from '@/components/EnhancedCheckIn';

const Home = () => {
  const location = useLocation();
  const [posts] = useState([
    { id: 1, content: "Welcome to the optimized Facebook clone!", author: "<PERSON>", timestamp: new Date().toISOString() },
    { id: 2, content: "This is a sample post to test the interface.", author: "<PERSON>", timestamp: new Date().toISOString() }
  ]);
  const [isCheckInOpen, setIsCheckInOpen] = useState(false);

  // Handle navigation from notifications
  useEffect(() => {
    if (location.state?.fromNotification) {
      const { highlightPost, notificationContent, notificationType, userInfo } = location.state;
      
      if (highlightPost) {
        toast.success(`📍 Showing post: ${highlightPost}`);
        console.log('🎯 Highlighting post:', highlightPost);
        // Here you could scroll to and highlight the specific post
      } else if (notificationType && userInfo) {
        toast.success(`📱 Opened from ${notificationType} notification by ${userInfo.name}`);
        console.log('🔔 From notification:', { notificationType, userInfo });
      } else {
        toast.success('📱 Opened from notification');
      }
      
      // Clear the state to prevent repeated toasts
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location]);

  const handleCheckIn = (checkInData: CheckInData) => {
    // Save check-in to local storage
    const existingCheckIns = JSON.parse(localStorage.getItem('user_checkins') || '[]');
    const newCheckIn = {
      id: `checkin_${Date.now()}`,
      ...checkInData,
      timestamp: new Date().toISOString()
    };

    const updatedCheckIns = [newCheckIn, ...existingCheckIns].slice(0, 50); // Keep last 50 check-ins
    localStorage.setItem('user_checkins', JSON.stringify(updatedCheckIns));

    // Show success message with location details
    toast.success(`✅ Checked in at ${checkInData.location.name}!`, {
      description: checkInData.message ? `"${checkInData.message}"` : `${checkInData.location.address}`,
      duration: 4000
    });

    // Create a post about the check-in if sharing is enabled
    if (checkInData.shareToStory || checkInData.privacy !== 'only_me') {
      const checkInPost = {
        id: `post_${Date.now()}`,
        content: `📍 ${checkInData.message || `Checked in at ${checkInData.location.name}`}`,
        location: checkInData.location.name,
        feeling: checkInData.feeling,
        privacy: checkInData.privacy,
        created_at: new Date().toISOString(),
        author: 'You',
        timestamp: new Date().toISOString()
      };

      // You could add this to your posts state if needed
      console.log('Check-in post created:', checkInPost);
    }

    console.log('Check-in saved:', newCheckIn);
  };

  return (
    <div className="max-w-2xl mx-auto space-y-4">
      {/* Stories */}
      <Stories />
      
      {/* Create Post */}
      <CreatePost />
      
      {/* Posts */}
      <div className="space-y-4">
        {posts.map((post) => (
          <PostCard
            key={post.id}
            post={post}
          />
        ))}
      </div>

      {/* Floating Check-in Button */}
      <Button
        onClick={() => setIsCheckInOpen(true)}
        className="fixed bottom-6 right-6 z-50 h-14 w-14 rounded-full bg-emerald-600 hover:bg-emerald-700 shadow-lg hover:shadow-xl transition-all duration-200 md:hidden"
        size="icon"
      >
        <MapPin className="w-6 h-6" />
      </Button>

      {/* Enhanced Check-in Modal */}
      <EnhancedCheckIn
        isOpen={isCheckInOpen}
        onClose={() => setIsCheckInOpen(false)}
        onCheckIn={handleCheckIn}
      />
    </div>
  );
};

export default Home;