import { Location } from '@/services/SocialFeaturesService';

export const CATEGORIES = [
  { id: 'all', name: 'All Categories', icon: '🏪' },
  { id: 'vehicles', name: 'Vehicles', icon: '🚗' },
  { id: 'electronics', name: 'Electronics', icon: '📱' },
  { id: 'home', name: 'Home & Garden', icon: '🏠' },
  { id: 'clothing', name: 'Clothing & Accessories', icon: '👕' },
  { id: 'sports', name: 'Sports & Outdoors', icon: '⚽' },
  { id: 'books', name: 'Books & Media', icon: '📚' },
  { id: 'toys', name: 'Toys & Games', icon: '🧸' },
  { id: 'furniture', name: 'Furniture', icon: '🪑' },
  { id: 'tools', name: 'Tools & Equipment', icon: '🔧' },
  { id: 'other', name: 'Other', icon: '📦' }
] as const;

export const CONDITIONS = [
  { id: 'all', name: 'All Conditions' },
  { id: 'new', name: 'New' },
  { id: 'used', name: 'Used - Like New' },
  { id: 'good', name: 'Used - Good' },
  { id: 'fair', name: 'Used - Fair' },
  { id: 'refurbished', name: 'Refurbished' }
] as const;

export interface MarketplaceFilters {
  category: string;
  condition: string;
  priceMin: number;
  priceMax: number;
  location: string;
  radius: number;
  sortBy: 'recent' | 'price_low' | 'price_high' | 'distance';
  deliveryOptions: string[];
}

export const INITIAL_FILTERS: MarketplaceFilters = {
  category: 'all',
  condition: 'all',
  priceMin: 0,
  priceMax: 10000,
  location: '',
  radius: 25,
  sortBy: 'recent',
  deliveryOptions: []
};

export const INITIAL_NEW_LISTING = {
  title: '',
  description: '',
  price: '',
  category: '',
  condition: 'used' as 'new' | 'used' | 'refurbished',
  photos: [] as string[],
  location: null as Location | null,
  deliveryOptions: ['pickup'] as string[],
  contactInfo: {
    phone: '',
    email: '',
    messenger: true
  }
};

// Response templates for seller automation
export const SELLER_RESPONSE_TEMPLATES = {
  available: "Yes, it's still available! Would you like to know more details?",
  price: "The price is firm, but I'm open to reasonable offers. When would you like to see it?",
  condition: "It's in excellent condition as described. I can send more photos if you'd like.",
  meetup: "Sure! I'm available most evenings and weekends. What works best for you?",
  greeting: "Hello! Thanks for your interest. What would you like to know?",
  default: "Thanks for your message! I'll get back to you shortly with more details."
} as const;
