import React, { useState, useRef, useCallback } from 'react';
import {
  Send,
  Smile,
  Paperclip,
  Image,
  Video,
  Mic,
  MapPin,
  Users,
  Globe,
  Lock,
  X,
  Search,
  Camera
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
// Simple dropdown replacement with select
const DropdownMenu: React.FC<{ children: React.ReactNode }> = ({ children }) => <div className="relative">{children}</div>;
const DropdownMenuTrigger: React.FC<{ asChild?: boolean; children: React.ReactNode }> = ({ children }) => <>{children}</>;
const DropdownMenuContent: React.FC<{ children: React.ReactNode }> = ({ children }) => <div className="absolute top-full left-0 bg-white border rounded-lg shadow-lg z-10">{children}</div>;
const DropdownMenuItem: React.FC<{ onClick?: () => void; children: React.ReactNode }> = ({ onClick, children }) => (
  <button onClick={onClick} className="w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors">{children}</button>
);
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import AdvancedFileUpload from '@/components/AdvancedFileUpload';
import { UploadFile } from '@/services/FileUploadService';

// Enhanced Message Composer
interface MessageComposerProps {
  placeholder?: string;
  onSend: (message: string, attachments?: File[]) => void;
  onTyping?: (isTyping: boolean) => void;
  allowAttachments?: boolean;
  allowEmoji?: boolean;
  allowVoice?: boolean;
  maxLength?: number;
  className?: string;
  disabled?: boolean;
}

export const MessageComposer: React.FC<MessageComposerProps> = React.memo(({
  placeholder = "Type a message...",
  onSend,
  onTyping,
  allowAttachments = true,
  allowEmoji = true,
  allowVoice = true,
  maxLength = 1000,
  className,
  disabled = false
}) => {
  const [message, setMessage] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isRecording, setIsRecording] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  const handleMessageChange = useCallback((value: string) => {
    setMessage(value);
    
    // Handle typing indicator
    if (onTyping) {
      onTyping(true);
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
      typingTimeoutRef.current = setTimeout(() => {
        onTyping(false);
      }, 1000);
    }

    // Auto-resize textarea
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [onTyping]);

  const handleSend = useCallback(() => {
    if (message.trim() || attachments.length > 0) {
      onSend(message.trim(), attachments);
      setMessage('');
      setAttachments([]);
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  }, [message, attachments, onSend]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  }, [handleSend]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  }, []);

  const removeAttachment = useCallback((index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }, []);

  const startRecording = useCallback(() => {
    setIsRecording(true);
    // Implement voice recording logic
    toast.info('Voice recording started');
  }, []);

  const stopRecording = useCallback(() => {
    setIsRecording(false);
    // Implement voice recording logic
    toast.info('Voice recording stopped');
  }, []);

  return (
    <Card className={cn("border-0 shadow-none", className)}>
      <CardContent className="p-4">
        {/* Attachments Preview */}
        {attachments.length > 0 && (
          <div className="mb-3 flex flex-wrap gap-2">
            {attachments.map((file, index) => (
              <Badge key={index} variant="secondary" className="flex items-center space-x-2">
                <span className="text-xs">{file.name}</span>
                <button
                  onClick={() => removeAttachment(index)}
                  className="text-gray-500 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}

        <div className="flex items-end space-x-2">
          {/* Attachment Button */}
          {allowAttachments && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" disabled={disabled}>
                  <Paperclip className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => fileInputRef.current?.click()}>
                  <Image className="w-4 h-4 mr-2" />
                  Photo
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => fileInputRef.current?.click()}>
                  <Video className="w-4 h-4 mr-2" />
                  Video
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <MapPin className="w-4 h-4 mr-2" />
                  Location
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}

          {/* Message Input */}
          <div className="flex-1">
            <Textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => handleMessageChange(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={placeholder}
              disabled={disabled}
              maxLength={maxLength}
              className="min-h-[40px] max-h-32 resize-none border-0 shadow-none focus-visible:ring-0"
              rows={1}
            />
          </div>

          {/* Emoji Button */}
          {allowEmoji && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowEmojiPicker(!showEmojiPicker)}
              disabled={disabled}
            >
              <Smile className="w-4 h-4" />
            </Button>
          )}

          {/* Voice/Send Button */}
          {message.trim() || attachments.length > 0 ? (
            <Button onClick={handleSend} size="sm" disabled={disabled}>
              <Send className="w-4 h-4" />
            </Button>
          ) : allowVoice ? (
            <Button
              variant={isRecording ? "destructive" : "ghost"}
              size="sm"
              onMouseDown={startRecording}
              onMouseUp={stopRecording}
              onMouseLeave={stopRecording}
              disabled={disabled}
            >
              <Mic className="w-4 h-4" />
            </Button>
          ) : null}
        </div>

        {/* Character Count */}
        {maxLength && message.length > maxLength * 0.8 && (
          <div className="text-xs text-gray-500 mt-2 text-right">
            {message.length}/{maxLength}
          </div>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </CardContent>
    </Card>
  );
});

// Enhanced Post Creator
interface PostCreatorProps {
  user: {
    id: string;
    name: string;
    avatar: string;
  };
  onCreatePost: (post: {
    content: string;
    attachments?: File[];
    uploadedFiles?: UploadFile[];
    feeling?: string;
    location?: string;
    privacy?: string;
    taggedFriends?: string[];
  }) => void;
  placeholder?: string;
  allowMedia?: boolean;
  allowLocation?: boolean;
  allowTagging?: boolean;
  allowFeeling?: boolean;
  className?: string;
}

export const PostCreator: React.FC<PostCreatorProps> = ({
  user,
  onCreatePost,
  placeholder = "What's on your mind?",
  allowMedia = true,
  allowLocation = true,
  allowTagging = true,
  allowFeeling = true,
  className
}) => {
  const [content, setContent] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<UploadFile[]>([]);
  const [feeling, setFeeling] = useState<string>('');
  const [location, setLocation] = useState<string>('');
  const [privacy, setPrivacy] = useState<string>('friends');
  const [taggedFriends, setTaggedFriends] = useState<string[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showFileUpload, setShowFileUpload] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = useCallback(() => {
    if (content.trim() || attachments.length > 0 || uploadedFiles.length > 0) {
      onCreatePost({
        content: content.trim(),
        attachments,
        uploadedFiles,
        feeling,
        location,
        privacy,
        taggedFriends
      });

      // Reset form
      setContent('');
      setAttachments([]);
      setUploadedFiles([]);
      setFeeling('');
      setLocation('');
      setTaggedFriends([]);
      setIsExpanded(false);
      setShowFileUpload(false);
    }
  }, [content, attachments, uploadedFiles, feeling, location, privacy, taggedFriends, onCreatePost]);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setAttachments(prev => [...prev, ...files]);
    setIsExpanded(true);
  }, []);

  const removeAttachment = useCallback((index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  }, []);

  const handleFilesUploaded = useCallback((files: UploadFile[]) => {
    setUploadedFiles(prev => [...prev, ...files]);
    setIsExpanded(true);
    toast.success(`${files.length} file(s) uploaded successfully`);
  }, []);

  const handleAdvancedUpload = useCallback(() => {
    setShowFileUpload(true);
    setIsExpanded(true);
  }, []);

  const privacyOptions = [
    { value: 'public', label: 'Public', icon: Globe },
    { value: 'friends', label: 'Friends', icon: Users },
    { value: 'private', label: 'Only me', icon: Lock }
  ];

  const currentPrivacy = privacyOptions.find(option => option.value === privacy);

  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={user.avatar} />
            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <Textarea
              value={content}
              onChange={(e) => {
                setContent(e.target.value);
                if (!isExpanded && e.target.value.length > 0) {
                  setIsExpanded(true);
                }
              }}
              placeholder={placeholder}
              className="min-h-[60px] border-0 shadow-none resize-none focus-visible:ring-0"
              rows={isExpanded ? 4 : 2}
            />

            {/* Attachments Preview */}
            {attachments.length > 0 && (
              <div className="mt-3 grid grid-cols-2 md:grid-cols-3 gap-2">
                {attachments.map((file, index) => (
                  <div key={index} className="relative">
                    {file.type.startsWith('image/') ? (
                      <img
                        src={URL.createObjectURL(file)}
                        alt="Preview"
                        className="w-full h-24 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="w-full h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                        <Video className="w-8 h-8 text-gray-400" />
                      </div>
                    )}
                    <button
                      onClick={() => removeAttachment(index)}
                      className="absolute top-1 right-1 w-6 h-6 bg-black/50 rounded-full flex items-center justify-center text-white hover:bg-black/70"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Advanced File Upload */}
            {showFileUpload && (
              <div className="mt-3">
                <AdvancedFileUpload
                  onFilesUploaded={handleFilesUploaded}
                  options={{
                    maxFileSize: 50 * 1024 * 1024, // 50MB
                    allowedTypes: ['image/*', 'video/*', 'audio/*', 'application/pdf'],
                    maxFiles: 10,
                    autoCompress: true,
                    compressionQuality: 0.8,
                    generateThumbnails: true
                  }}
                  multiple={true}
                  accept="image/*,video/*,audio/*,application/pdf"
                  className="border-dashed border-2 border-gray-300"
                />
              </div>
            )}

            {/* Uploaded Files Display */}
            {uploadedFiles.length > 0 && (
              <div className="mt-3">
                <h4 className="text-sm font-medium mb-2">Uploaded Files</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {uploadedFiles.map((uploadFile) => (
                    <div key={uploadFile.id} className="relative">
                      {uploadFile.preview ? (
                        <img
                          src={uploadFile.preview}
                          alt={uploadFile.name}
                          className="w-full h-24 object-cover rounded"
                        />
                      ) : (
                        <div className="w-full h-24 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-xs text-gray-500">{uploadFile.name}</span>
                        </div>
                      )}
                      <button
                        onClick={() => setUploadedFiles(prev => prev.filter(f => f.id !== uploadFile.id))}
                        className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Additional Options */}
            {isExpanded && (
              <div className="mt-3 space-y-3">
                {/* Feeling and Location */}
                <div className="flex flex-wrap gap-2">
                  {feeling && (
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Smile className="w-3 h-3" />
                      <span>{feeling}</span>
                      <button onClick={() => setFeeling('')}>
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {location && (
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <MapPin className="w-3 h-3" />
                      <span>{location}</span>
                      <button onClick={() => setLocation('')}>
                        <X className="w-3 h-3" />
                      </button>
                    </Badge>
                  )}
                  {taggedFriends.length > 0 && (
                    <Badge variant="secondary" className="flex items-center space-x-1">
                      <Users className="w-3 h-3" />
                      <span>{taggedFriends.length} friend{taggedFriends.length !== 1 ? 's' : ''}</span>
                    </Badge>
                  )}
                </div>

                {/* Action Buttons and Privacy */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {allowMedia && (
                      <>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Camera className="w-4 h-4 mr-2" />
                          Photo/Video
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={handleAdvancedUpload}
                        >
                          <Paperclip className="w-4 h-4 mr-2" />
                          Advanced Upload
                        </Button>
                      </>
                    )}
                    
                    {allowFeeling && (
                      <Button variant="ghost" size="sm">
                        <Smile className="w-4 h-4 mr-2" />
                        Feeling
                      </Button>
                    )}
                    
                    {allowLocation && (
                      <Button variant="ghost" size="sm">
                        <MapPin className="w-4 h-4 mr-2" />
                        Location
                      </Button>
                    )}
                    
                    {allowTagging && (
                      <Button variant="ghost" size="sm">
                        <Users className="w-4 h-4 mr-2" />
                        Tag Friends
                      </Button>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Privacy Selector */}
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm" className="flex items-center space-x-2">
                          {currentPrivacy && <currentPrivacy.icon className="w-4 h-4" />}
                          <span>{currentPrivacy?.label}</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        {privacyOptions.map((option) => (
                          <DropdownMenuItem
                            key={option.value}
                            onClick={() => setPrivacy(option.value)}
                          >
                            <option.icon className="w-4 h-4 mr-2" />
                            {option.label}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>

                    <Button
                      onClick={handleSubmit}
                      disabled={!content.trim() && attachments.length === 0 && uploadedFiles.length === 0}
                    >
                      Post
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions (when not expanded) */}
        {!isExpanded && (
          <div className="flex items-center justify-between mt-3 pt-3 border-t">
            <div className="flex items-center space-x-4">
              {allowMedia && (
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <Camera className="w-5 h-5" />
                  <span className="text-sm">Photo/Video</span>
                </button>
              )}
              
              {allowFeeling && (
                <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors">
                  <Smile className="w-5 h-5" />
                  <span className="text-sm">Feeling</span>
                </button>
              )}
            </div>
          </div>
        )}

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,video/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </CardContent>
    </Card>
  );
};

// Search Input Component
interface SearchInputProps {
  placeholder?: string;
  onSearch: (query: string) => void;
  onClear?: () => void;
  suggestions?: string[];
  showSuggestions?: boolean;
  className?: string;
  debounceMs?: number;
}

export const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = "Search...",
  onSearch,
  onClear,
  suggestions = [],
  showSuggestions = false,
  className,
  debounceMs = 300
}) => {
  const [query, setQuery] = useState('');
  const [showSuggestionsList, setShowSuggestionsList] = useState(false);
  const debounceRef = useRef<NodeJS.Timeout>();

  const handleInputChange = useCallback((value: string) => {
    setQuery(value);
    
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }
    
    debounceRef.current = setTimeout(() => {
      onSearch(value);
    }, debounceMs);
  }, [onSearch, debounceMs]);

  const handleClear = useCallback(() => {
    setQuery('');
    setShowSuggestionsList(false);
    onSearch('');
    onClear?.();
  }, [onSearch, onClear]);

  const handleSuggestionClick = useCallback((suggestion: string) => {
    setQuery(suggestion);
    setShowSuggestionsList(false);
    onSearch(suggestion);
  }, [onSearch]);

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <Input
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onFocus={() => setShowSuggestionsList(showSuggestions && suggestions.length > 0)}
          placeholder={placeholder}
          className="pl-10 pr-10"
        />
        {query && (
          <button
            onClick={handleClear}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      {/* Suggestions Dropdown */}
      {showSuggestionsList && suggestions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto">
          {suggestions.map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="w-full text-left px-4 py-2 hover:bg-gray-50 transition-colors"
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};
