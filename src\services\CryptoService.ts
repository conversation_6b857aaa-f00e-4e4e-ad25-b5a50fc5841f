// End-to-End Encryption Service for Messaging
export class CryptoService {
  private keyPairs = new Map<string, CryptoKeyPair>();
  private sharedKeys = new Map<string, CryptoKey>();
  private masterKey: CryptoKey | null = null;

  constructor() {
    this.initializeMasterKey();
  }

  private async initializeMasterKey() {
    try {
      // Try to load existing master key from secure storage
      const storedKey = await this.getStoredMasterKey();
      if (storedKey) {
        this.masterKey = storedKey;
      } else {
        // Generate new master key
        this.masterKey = await this.generateMasterKey();
        await this.storeMasterKey(this.masterKey);
      }
    } catch (error) {
      console.error('Failed to initialize master key:', error);
      // Fallback to generating a new key
      this.masterKey = await this.generateMasterKey();
    }
  }

  private async generateMasterKey(): Promise<CryptoKey> {
    return await window.crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );
  }

  private async getStoredMasterKey(): Promise<CryptoKey | null> {
    try {
      const keyData = localStorage.getItem('messenger_master_key');
      if (!keyData) return null;

      const keyArrayBuffer = this.base64ToArrayBuffer(keyData);
      return await window.crypto.subtle.importKey(
        'raw',
        keyArrayBuffer,
        'AES-GCM',
        true,
        ['encrypt', 'decrypt']
      );
    } catch (error) {
      console.error('Failed to load stored master key:', error);
      return null;
    }
  }

  private async storeMasterKey(key: CryptoKey) {
    try {
      const keyData = await window.crypto.subtle.exportKey('raw', key);
      const keyBase64 = this.arrayBufferToBase64(keyData);
      localStorage.setItem('messenger_master_key', keyBase64);
    } catch (error) {
      console.error('Failed to store master key:', error);
    }
  }

  // Generate key pair for user
  async generateUserKeyPair(userId: string): Promise<CryptoKeyPair> {
    try {
      const keyPair = await window.crypto.subtle.generateKey(
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        ['deriveKey', 'deriveBits']
      );

      this.keyPairs.set(userId, keyPair);
      await this.storeKeyPair(userId, keyPair);
      
      return keyPair;
    } catch (error) {
      console.error('Failed to generate key pair:', error);
      throw error;
    }
  }

  // Store key pair securely
  private async storeKeyPair(userId: string, keyPair: CryptoKeyPair) {
    try {
      const privateKey = await window.crypto.subtle.exportKey('pkcs8', keyPair.privateKey);
      const publicKey = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);

      // Encrypt private key with master key
      const encryptedPrivateKey = await this.encryptWithMasterKey(privateKey);

      localStorage.setItem(`messenger_private_key_${userId}`, this.arrayBufferToBase64(encryptedPrivateKey));
      localStorage.setItem(`messenger_public_key_${userId}`, this.arrayBufferToBase64(publicKey));
    } catch (error) {
      console.error('Failed to store key pair:', error);
    }
  }

  // Load key pair from storage
  async loadUserKeyPair(userId: string): Promise<CryptoKeyPair | null> {
    try {
      const encryptedPrivateKeyData = localStorage.getItem(`messenger_private_key_${userId}`);
      const publicKeyData = localStorage.getItem(`messenger_public_key_${userId}`);

      if (!encryptedPrivateKeyData || !publicKeyData) {
        return null;
      }

      // Decrypt private key
      const encryptedPrivateKey = this.base64ToArrayBuffer(encryptedPrivateKeyData);
      const privateKeyArrayBuffer = await this.decryptWithMasterKey(encryptedPrivateKey);

      const privateKey = await window.crypto.subtle.importKey(
        'pkcs8',
        privateKeyArrayBuffer,
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        ['deriveKey', 'deriveBits']
      );

      const publicKey = await window.crypto.subtle.importKey(
        'spki',
        this.base64ToArrayBuffer(publicKeyData),
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        []
      );

      const keyPair = { privateKey, publicKey };
      this.keyPairs.set(userId, keyPair);
      
      return keyPair;
    } catch (error) {
      console.error('Failed to load key pair:', error);
      return null;
    }
  }

  // Derive shared key for conversation
  async deriveSharedKey(conversationId: string, otherUserPublicKey: CryptoKey, myUserId: string): Promise<CryptoKey> {
    try {
      let myKeyPair = this.keyPairs.get(myUserId);
      
      if (!myKeyPair) {
        myKeyPair = await this.loadUserKeyPair(myUserId);
        if (!myKeyPair) {
          myKeyPair = await this.generateUserKeyPair(myUserId);
        }
      }

      const sharedKey = await window.crypto.subtle.deriveKey(
        {
          name: 'ECDH',
          public: otherUserPublicKey
        },
        myKeyPair.privateKey,
        {
          name: 'AES-GCM',
          length: 256
        },
        false,
        ['encrypt', 'decrypt']
      );

      this.sharedKeys.set(conversationId, sharedKey);
      return sharedKey;
    } catch (error) {
      console.error('Failed to derive shared key:', error);
      throw error;
    }
  }

  // Encrypt message for conversation
  async encryptMessage(message: string, conversationId: string): Promise<string> {
    try {
      let sharedKey = this.sharedKeys.get(conversationId);
      
      if (!sharedKey) {
        // For demo purposes, use master key if shared key not available
        sharedKey = this.masterKey!;
      }

      const encoder = new TextEncoder();
      const data = encoder.encode(message);
      
      // Generate random IV
      const iv = window.crypto.getRandomValues(new Uint8Array(12));
      
      const encryptedData = await window.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        sharedKey,
        data
      );

      // Combine IV and encrypted data
      const result = new Uint8Array(iv.length + encryptedData.byteLength);
      result.set(iv, 0);
      result.set(new Uint8Array(encryptedData), iv.length);

      return this.arrayBufferToBase64(result);
    } catch (error) {
      console.error('Failed to encrypt message:', error);
      throw error;
    }
  }

  // Decrypt message for conversation
  async decryptMessage(encryptedMessage: string, conversationId: string): Promise<string> {
    try {
      let sharedKey = this.sharedKeys.get(conversationId);
      
      if (!sharedKey) {
        // For demo purposes, use master key if shared key not available
        sharedKey = this.masterKey!;
      }

      const encryptedData = this.base64ToArrayBuffer(encryptedMessage);
      
      // Extract IV and encrypted content
      const iv = encryptedData.slice(0, 12);
      const data = encryptedData.slice(12);

      const decryptedData = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        sharedKey,
        data
      );

      const decoder = new TextDecoder();
      return decoder.decode(decryptedData);
    } catch (error) {
      console.error('Failed to decrypt message:', error);
      // Return encrypted message as fallback
      return '[Encrypted Message]';
    }
  }

  // Encrypt file/attachment
  async encryptFile(file: ArrayBuffer, conversationId: string): Promise<ArrayBuffer> {
    try {
      let sharedKey = this.sharedKeys.get(conversationId);
      
      if (!sharedKey) {
        sharedKey = this.masterKey!;
      }

      const iv = window.crypto.getRandomValues(new Uint8Array(12));
      
      const encryptedData = await window.crypto.subtle.encrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        sharedKey,
        file
      );

      // Combine IV and encrypted data
      const result = new Uint8Array(iv.length + encryptedData.byteLength);
      result.set(iv, 0);
      result.set(new Uint8Array(encryptedData), iv.length);

      return result.buffer;
    } catch (error) {
      console.error('Failed to encrypt file:', error);
      throw error;
    }
  }

  // Decrypt file/attachment
  async decryptFile(encryptedFile: ArrayBuffer, conversationId: string): Promise<ArrayBuffer> {
    try {
      let sharedKey = this.sharedKeys.get(conversationId);
      
      if (!sharedKey) {
        sharedKey = this.masterKey!;
      }

      const encryptedData = new Uint8Array(encryptedFile);
      const iv = encryptedData.slice(0, 12);
      const data = encryptedData.slice(12);

      const decryptedData = await window.crypto.subtle.decrypt(
        {
          name: 'AES-GCM',
          iv: iv
        },
        sharedKey,
        data
      );

      return decryptedData;
    } catch (error) {
      console.error('Failed to decrypt file:', error);
      throw error;
    }
  }

  // Export public key for sharing
  async exportPublicKey(userId: string): Promise<string> {
    try {
      let keyPair = this.keyPairs.get(userId);
      
      if (!keyPair) {
        keyPair = await this.loadUserKeyPair(userId);
        if (!keyPair) {
          keyPair = await this.generateUserKeyPair(userId);
        }
      }

      const publicKeyData = await window.crypto.subtle.exportKey('spki', keyPair.publicKey);
      return this.arrayBufferToBase64(publicKeyData);
    } catch (error) {
      console.error('Failed to export public key:', error);
      throw error;
    }
  }

  // Import public key from other user
  async importPublicKey(publicKeyBase64: string): Promise<CryptoKey> {
    try {
      const publicKeyData = this.base64ToArrayBuffer(publicKeyBase64);
      
      return await window.crypto.subtle.importKey(
        'spki',
        publicKeyData,
        {
          name: 'ECDH',
          namedCurve: 'P-256'
        },
        true,
        []
      );
    } catch (error) {
      console.error('Failed to import public key:', error);
      throw error;
    }
  }

  // Utility methods for master key encryption
  private async encryptWithMasterKey(data: ArrayBuffer): Promise<ArrayBuffer> {
    if (!this.masterKey) {
      throw new Error('Master key not initialized');
    }

    const iv = window.crypto.getRandomValues(new Uint8Array(12));
    
    const encryptedData = await window.crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      this.masterKey,
      data
    );

    const result = new Uint8Array(iv.length + encryptedData.byteLength);
    result.set(iv, 0);
    result.set(new Uint8Array(encryptedData), iv.length);

    return result.buffer;
  }

  private async decryptWithMasterKey(encryptedData: ArrayBuffer): Promise<ArrayBuffer> {
    if (!this.masterKey) {
      throw new Error('Master key not initialized');
    }

    const encryptedArray = new Uint8Array(encryptedData);
    const iv = encryptedArray.slice(0, 12);
    const data = encryptedArray.slice(12);

    return await window.crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      this.masterKey,
      data
    );
  }

  // Utility methods for base64 conversion
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  // Clean up
  destroy() {
    this.keyPairs.clear();
    this.sharedKeys.clear();
    this.masterKey = null;
  }
}
