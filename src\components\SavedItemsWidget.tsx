import React from 'react';
import { Bookmark, ExternalLink, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useSavedItems } from '@/hooks/useSavedItems';
import { formatTimeAgo } from '@/lib/utils';
import OptimizedImage from '@/components/OptimizedImage';

const SavedItemsWidget = () => {
  const navigate = useNavigate();
  const { items, isLoading } = useSavedItems();

  const handleViewItem = (itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      toast.success(`Viewing ${item.title}`);
    }
  };

  const handleViewAll = () => {
    navigate('/saved');
  };

  if (isLoading) {
    return (
      <Card className="card-responsive bg-white dark:bg-gray-800 border-0 shadow-sm hover:shadow-md transition-shadow">
        <CardHeader className="p-3">
          <CardTitle className="text-base font-semibold flex items-center justify-between">
            <div className="flex items-center">
              <Bookmark className="w-5 h-5 mr-2" />
              <span>Saved Items</span>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-2">
          <div className="space-y-3">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="flex space-x-3 p-2 animate-pulse">
                <div className="w-12 h-12 bg-gray-200 rounded-lg dark:bg-gray-700"></div>
                <div className="flex-1 min-w-0">
                  <div className="h-4 bg-gray-200 rounded mb-2 dark:bg-gray-700"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 dark:bg-gray-700"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="card-responsive bg-white dark:bg-gray-800 border-0 shadow-sm hover:shadow-md transition-shadow">
      <CardHeader className="p-3">
        <CardTitle className="text-base font-semibold flex items-center justify-between">
          <div className="flex items-center">
            <Bookmark className="w-5 h-5 mr-2" />
            <span>Saved Items</span>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleViewAll}
            className="text-blue-600 text-xs dark:text-blue-400"
          >
            See All
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-2">
        <div className="space-y-3">
          {items.length > 0 ? items.slice(0, 3).map((item) => (
            <div 
              key={item.id} 
              className="flex space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors dark:hover:bg-gray-700"
              onClick={() => handleViewItem(item.id)}
            >
              {item.image ? (
                <OptimizedImage
                  src={item.image}
                  alt={item.title}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              ) : (
                <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center dark:bg-gray-700">
                  <Bookmark className="w-6 h-6 text-gray-500 dark:text-gray-400" />
                </div>
              )}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm text-gray-900 truncate dark:text-gray-100">{item.title}</h4>
                <div className="flex items-center mt-1">
                  <Clock className="w-3 h-3 text-gray-500 mr-1 dark:text-gray-400" />
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    Saved {formatTimeAgo(item.savedDate)}
                  </span>
                </div>
              </div>
              {item.url && (
                <ExternalLink className="w-4 h-4 text-gray-400 dark:text-gray-500" />
              )}
            </div>
          )) : (
            <div className="text-center text-gray-500 text-sm py-4 dark:text-gray-400">
              No saved items yet. Save posts from your feed to see them here!
            </div>
          )}
          
          {items.length > 0 && (
            <Button 
              variant="ghost" 
              size="sm" 
              className="w-full mt-3 text-blue-600 dark:text-blue-400"
              onClick={handleViewAll}
            >
              View All Saved Items
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SavedItemsWidget;
