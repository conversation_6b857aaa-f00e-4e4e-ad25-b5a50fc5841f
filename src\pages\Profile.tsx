import React, { useEffect } from 'react';
import ProfileTab from '@/components/ProfileTab';
import { useLocation } from 'react-router-dom';
import { toast } from 'sonner';

const Profile = () => {
  const location = useLocation();

  // Handle navigation from notifications
  useEffect(() => {
    if (location.state?.fromNotification) {
      const { userId, userName, showBirthday } = location.state;
      
      if (showBirthday) {
        toast.success(`🎂 ${userName}'s Birthday!`);
        console.log('🎂 Birthday notification for:', userName);
      } else if (userName) {
        toast.success(`👤 Viewing ${userName}'s profile`);
        console.log('👤 Profile from notification:', { userId, userName });
      } else {
        toast.success('👤 Viewing profile from notification');
      }
      
      // Clear the state to prevent repeated toasts
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location]);

  return (
    <div className="w-full">
      <ProfileTab />
    </div>
  );
};

export default Profile;