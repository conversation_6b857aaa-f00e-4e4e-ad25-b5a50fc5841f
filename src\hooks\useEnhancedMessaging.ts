import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Message, Conversation, CallData,
  ChatSettings, MessageSearchResult, MessageDraft
} from '@/types/enhanced-messaging';
import { getEnhancedMessagingService, EnhancedMessagingService } from '@/services/EnhancedMessagingService';
import { toast } from 'sonner';

interface UseEnhancedMessagingReturn {
  // State
  conversations: Conversation[];
  activeConversation: Conversation | null;
  messages: Message[];
  isConnected: boolean;
  isTyping: boolean;
  typingUsers: string[];
  searchResults: MessageSearchResult[];
  currentCall: CallData | null;
  settings: ChatSettings;
  
  // Actions
  selectConversation: (conversationId: string) => void;
  sendMessage: (content: string, options?: {
    type?: Message['type'];
    replyTo?: string;
    attachments?: File[];
    mentions?: string[];
  }) => Promise<void>;
  editMessage: (messageId: string, newContent: string) => Promise<void>;
  deleteMessage: (messageId: string, deleteForEveryone?: boolean) => Promise<void>;
  forwardMessage: (messageId: string, conversationIds: string[]) => Promise<void>;
  addReaction: (messageId: string, emoji: string) => Promise<void>;
  
  // Search
  searchMessages: (query: string, filters?: {
    conversationId?: string;
    senderId?: string;
    type?: Message['type'];
    dateFrom?: Date;
    dateTo?: Date;
  }) => Promise<void>;
  clearSearch: () => void;
  
  // Calls
  startCall: (type: 'audio' | 'video') => Promise<void>;
  endCall: () => Promise<void>;
  
  // Threads
  createThread: (messageId: string) => Promise<void>;
  replyToThread: (parentMessageId: string, content: string) => Promise<void>;
  
  // Drafts
  saveDraft: (content: string, replyTo?: string) => void;
  getDraft: () => MessageDraft | null;
  clearDraft: () => void;
  
  // Settings
  updateSettings: (newSettings: Partial<ChatSettings>) => void;
  
  // Typing
  setTyping: (isTyping: boolean) => void;
  
  // Utilities
  markAsRead: (messageId: string) => void;
  loadMoreMessages: () => Promise<void>;
}

export const useEnhancedMessaging = (currentUserId: string): UseEnhancedMessagingReturn => {
  // State
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [isConnected, setIsConnected] = useState(true); // Start as connected for demo
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<MessageSearchResult[]>([]);
  const [currentCall, setCurrentCall] = useState<CallData | null>(null);
  const [settings, setSettings] = useState<ChatSettings>({
    theme: 'system',
    fontSize: 'medium',
    enterToSend: true,
    soundEnabled: true,
    notificationPreview: true,
    activeStatus: true,
    readReceipts: true,
    autoDownloadMedia: 'wifi',
    dataUsage: 'medium'
  });

  // Service reference
  const serviceRef = useRef<EnhancedMessagingService | null>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const activeConversationRef = useRef<Conversation | null>(null);

  // Helper functions (defined early to avoid hoisting issues)
  const playNotificationSound = useCallback(() => {
    if (settings.soundEnabled) {
      const audio = new Audio('/notification.mp3');
      audio.play().catch(console.error);
    }
  }, [settings.soundEnabled]);

  // Actions
  const selectConversation = useCallback(async (conversationId: string) => {
    console.log('🔄 Selecting conversation:', conversationId);
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation || !serviceRef.current) return;
    
    setActiveConversation(conversation);
    
    // Load messages for this conversation
    const conversationMessages = serviceRef.current.getMessages(conversationId);
    console.log('📋 Loaded messages for conversation:', conversationId, 'Count:', conversationMessages.length);
    console.log('📋 Messages:', conversationMessages);
    setMessages(conversationMessages);
    
    // Mark as read
    // service.markConversationAsRead(conversationId);
  }, [conversations]);

  const showMessageNotification = useCallback((message: Message) => {
    if (!settings.notificationPreview) return;
    
    const conversation = conversations.find(c => c.id === message.conversationId);
    if (!conversation) return;
    
    const senderName = conversation.type === 'direct' 
      ? conversation.user?.name 
      : `${conversation.name}`;
    
    toast.info(`${senderName}: ${message.content}`, {
      action: {
        label: 'Reply',
        onClick: () => selectConversation(message.conversationId)
      }
    });
  }, [conversations, settings.notificationPreview, selectConversation]);

  // Update the ref whenever activeConversation changes
  useEffect(() => {
    activeConversationRef.current = activeConversation;
  }, [activeConversation]);

  // Initialize service
  useEffect(() => {
    console.log('🔄 Initializing messaging service for user:', currentUserId);
    
    try {
      serviceRef.current = getEnhancedMessagingService(currentUserId);
      
      // Event listeners
      const service = serviceRef.current;
      
      service.on('connected', () => {
        console.log('✅ Messaging service connected event received');
        setIsConnected(true);
        toast.success('Connected to messaging service');
      });
      
      service.on('disconnected', () => {
        console.log('❌ Messaging service disconnected event received');
        setIsConnected(false);
        // Removed toast notification for disconnect to avoid popups when leaving tab
      });

      service.on('initialized', () => {
        console.log('🚀 Messaging service initialized event received');
        // Check initial connection status
        const connectionStatus = service.getConnectionStatus();
        console.log('📡 Connection status:', connectionStatus);
        setIsConnected(connectionStatus.connected);
        
        // Load conversations and messages when service is initialized
        const loadedConversations = service.getConversations();
        setConversations(loadedConversations);
        console.log(`✅ Loaded ${loadedConversations.length} conversations with mock data:`, loadedConversations);
        
        // Log users for debugging
        const users = service.getUsers();
        console.log(`👥 Available users:`, users.map(u => ({ id: u.id, name: u.name, isOnline: u.isOnline })));
      });
      
      service.on('messageReceived', (message: Message) => {
        if (activeConversationRef.current?.id === message.conversationId) {
          setMessages(prev => [...prev, message]);
        }
        
        // Play notification sound if enabled
        if (settings.soundEnabled && message.senderId !== currentUserId) {
          playNotificationSound();
        }
        
        // Show notification if not in active conversation
        if (activeConversationRef.current?.id !== message.conversationId && settings.notificationPreview) {
          showMessageNotification(message);
        }
      });

    service.on('messageAdded', (message: Message) => {
      console.log('📤 Message added event received:', message);
      if (activeConversationRef.current?.id === message.conversationId) {
        setMessages(prev => {
          // Check if message already exists to avoid duplicates
          const exists = prev.some(m => m.id === message.id);
          if (exists) {
            console.log('⚠️ Message already exists, skipping');
            return prev;
          }
          console.log('✅ Adding new message to conversation immediately');
          return [...prev, message];
        });
      }
    });
    
    service.on('messageSent', (message: Message) => {
      console.log('📤 Message sent event received:', message);
      if (activeConversationRef.current?.id === message.conversationId) {
        setMessages(prev => {
          // Check if message already exists to avoid duplicates
          const exists = prev.some(m => m.id === message.id);
          if (exists) {
            console.log('⚠️ Message already exists, updating status');
            return prev.map(m => m.id === message.id ? message : m);
          }
          console.log('✅ Adding new message to conversation');
          return [...prev, message];
        });
      }
    });
    
    service.on('messageEdited', (message: Message) => {
      if (activeConversationRef.current?.id === message.conversationId) {
        setMessages(prev => prev.map(m => m.id === message.id ? message : m));
      }
    });
    
    service.on('messageDeleted', (message: Message) => {
      if (activeConversationRef.current?.id === message.conversationId) {
        setMessages(prev => prev.map(m => m.id === message.id ? message : m));
      }
    });
    
    service.on('reactionAdded', ({ messageId: _messageId }: { messageId: string }) => {
      // Reactions are handled in the message object updates
      setMessages(prev => [...prev]); // Force re-render
    });
    
    service.on('conversationUpdated', (conversation: Conversation) => {
      // Refresh the entire conversations list to ensure proper sorting
      const updatedConversations = service.getConversations();
      console.log('🔄 Conversation updated, refreshing list. Updated conversation:', conversation.id);
      setConversations(updatedConversations);
      
      if (activeConversationRef.current?.id === conversation.id) {
        setActiveConversation(conversation);
      }
    });
    
    service.on('typingIndicator', (data: { conversationId: string; userId: string; isTyping: boolean }) => {
      const { conversationId, userId, isTyping: userIsTyping } = data;
      if (activeConversation?.id === conversationId && userId !== currentUserId) {
        setTypingUsers(prev => {
          if (userIsTyping) {
            return prev.includes(userId) ? prev : [...prev, userId];
          } else {
            return prev.filter(id => id !== userId);
          }
        });
      }
    });
    
    service.on('callStarted', (call: CallData) => {
      setCurrentCall(call);
    });
    
    service.on('callEnded', () => {
      setCurrentCall(null);
    });
    
    service.on('searchResults', (results: MessageSearchResult[]) => {
      setSearchResults(results);
    });
    
    service.on('settingsUpdated', (newSettings: ChatSettings) => {
      setSettings(newSettings);
    });
    
      // Load initial settings
      setSettings(service.getSettings());
      
      // Try to load conversations immediately as fallback
      try {
        const fallbackConversations = service.getConversations();
        if (fallbackConversations.length > 0) {
          console.log('🔄 Loading fallback conversations:', fallbackConversations.length);
          setConversations(fallbackConversations);
        }
      } catch (error) {
        console.warn('Could not load fallback conversations:', error);
      }
      
    } catch (error) {
      console.error('Failed to initialize messaging service:', error);
      setIsConnected(false);
      toast.error('Failed to initialize messaging service');
    }
    
    return () => {
      if (serviceRef.current) {
        serviceRef.current.disconnect();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentUserId]); // Only depend on currentUserId to avoid constant reconnections

  const sendMessage = useCallback(async (
    content: string, 
    options: {
      type?: Message['type'];
      replyTo?: string;
      attachments?: File[];
      mentions?: string[];
    } = {}
  ) => {
    if (!activeConversation || !serviceRef.current) {
      console.log('❌ Cannot send message - no active conversation or service');
      return;
    }
    
    console.log('📤 Sending message:', content, 'to conversation:', activeConversation.id);
    
    try {
      const message = await serviceRef.current.sendMessage(activeConversation.id, content, options);
      console.log('📤 Message sent successfully:', message);
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  }, [activeConversation]);

  const editMessage = useCallback(async (messageId: string, newContent: string) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.editMessage(messageId, newContent);
    } catch (error) {
      console.error('Failed to edit message:', error);
      toast.error('Failed to edit message');
    }
  }, []);

  const deleteMessage = useCallback(async (messageId: string, deleteForEveryone = false) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.deleteMessage(messageId, deleteForEveryone);
    } catch (error) {
      console.error('Failed to delete message:', error);
      toast.error('Failed to delete message');
    }
  }, []);

  const forwardMessage = useCallback(async (messageId: string, conversationIds: string[]) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.forwardMessage(messageId, conversationIds);
      toast.success(`Message forwarded to ${conversationIds.length} conversation(s)`);
    } catch (error) {
      console.error('Failed to forward message:', error);
      toast.error('Failed to forward message');
    }
  }, []);

  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.addReaction(messageId, emoji);
    } catch (error) {
      console.error('Failed to add reaction:', error);
      toast.error('Failed to add reaction');
    }
  }, []);

  // Search
  const searchMessages = useCallback(async (
    query: string, 
    filters: {
      conversationId?: string;
      senderId?: string;
      type?: Message['type'];
      dateFrom?: Date;
      dateTo?: Date;
    } = {}
  ) => {
    if (!serviceRef.current) return;
    
    try {
      const results = await serviceRef.current.searchMessages(query, filters);
      setSearchResults(results);
    } catch (error) {
      console.error('Failed to search messages:', error);
      toast.error('Failed to search messages');
    }
  }, []);

  const clearSearch = useCallback(() => {
    setSearchResults([]);
  }, []);

  // Calls
  const startCall = useCallback(async (type: 'audio' | 'video') => {
    if (!activeConversation || !serviceRef.current) return;
    
    try {
      await serviceRef.current.startCall(activeConversation.id, type);
    } catch (error) {
      console.error('Failed to start call:', error);
      toast.error('Failed to start call');
    }
  }, [activeConversation]);

  const endCall = useCallback(async () => {
    if (!currentCall || !serviceRef.current) return;
    
    try {
      await serviceRef.current.endCall(currentCall.id);
    } catch (error) {
      console.error('Failed to end call:', error);
      toast.error('Failed to end call');
    }
  }, [currentCall]);

  // Threads
  const createThread = useCallback(async (messageId: string) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.createThread(messageId);
    } catch (error) {
      console.error('Failed to create thread:', error);
      toast.error('Failed to create thread');
    }
  }, []);

  const replyToThread = useCallback(async (parentMessageId: string, content: string) => {
    if (!serviceRef.current) return;
    
    try {
      await serviceRef.current.replyToThread(parentMessageId, content);
    } catch (error) {
      console.error('Failed to reply to thread:', error);
      toast.error('Failed to reply to thread');
    }
  }, []);

  // Drafts
  const saveDraft = useCallback((content: string, replyTo?: string) => {
    if (!activeConversation || !serviceRef.current) return;
    serviceRef.current.saveDraft(activeConversation.id, content, replyTo);
  }, [activeConversation]);

  const getDraft = useCallback((): MessageDraft | null => {
    if (!activeConversation || !serviceRef.current) return null;
    return serviceRef.current.getDraft(activeConversation.id);
  }, [activeConversation]);

  const clearDraft = useCallback(() => {
    if (!activeConversation || !serviceRef.current) return;
    serviceRef.current.clearDraft(activeConversation.id);
  }, [activeConversation]);

  // Settings
  const updateSettings = useCallback((newSettings: Partial<ChatSettings>) => {
    if (!serviceRef.current) return;
    serviceRef.current.updateSettings(newSettings);
  }, []);

  // Typing
  const setTyping = useCallback((typing: boolean) => {
    setIsTyping(typing);
    
    if (!activeConversation || !serviceRef.current) return;
    
    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // Send typing indicator
    // serviceRef.current.sendTypingIndicator(activeConversation.id, typing);
    
    if (typing) {
      // Auto-stop typing after 3 seconds
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        // serviceRef.current?.sendTypingIndicator(activeConversation.id, false);
      }, 3000);
    }
  }, [activeConversation]);

  // Utilities
  const markAsRead = useCallback((messageId: string) => {
    // Implementation for marking message as read
    console.log('Marking message as read:', messageId);
  }, []);

  const loadMoreMessages = useCallback(async () => {
    if (!activeConversation) return;
    
    // Implementation for loading more messages
    console.log('Loading more messages for:', activeConversation.id);
  }, [activeConversation]);

  return {
    // State
    conversations,
    activeConversation,
    messages,
    isConnected,
    isTyping,
    typingUsers,
    searchResults,
    currentCall,
    settings,
    
    // Actions
    selectConversation,
    sendMessage,
    editMessage,
    deleteMessage,
    forwardMessage,
    addReaction,
    
    // Search
    searchMessages,
    clearSearch,
    
    // Calls
    startCall,
    endCall,
    
    // Threads
    createThread,
    replyToThread,
    
    // Drafts
    saveDraft,
    getDraft,
    clearDraft,
    
    // Settings
    updateSettings,
    
    // Typing
    setTyping,
    
    // Utilities
    markAsRead,
    loadMoreMessages
  };
};
