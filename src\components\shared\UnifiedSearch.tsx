import React, { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { Search, X, Filter, Clock, TrendingUp, Users, Hash, MapPin, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
// ScrollArea component not available, using div with overflow
import { motion, AnimatePresence } from 'framer-motion';
import { debounce } from '@/lib/utils';
import { storage } from '@/lib/storage';
import { BaseSearchResult } from '@/types/shared';
import { formatDistanceToNow } from 'date-fns';

interface SearchFilters {
  type: string[];
  dateRange: string;
  location: string;
  sortBy: string;
  hasMedia: boolean;
  verified: boolean;
}

interface UnifiedSearchProps {
  isOpen: boolean;
  onClose: () => void;
  onResultSelect: (result: BaseSearchResult) => void;
  initialQuery?: string;
  placeholder?: string;
  showFilters?: boolean;
  showHistory?: boolean;
  showTrending?: boolean;
  categories?: string[];
  className?: string;
}

const UnifiedSearch: React.FC<UnifiedSearchProps> = memo(({
  isOpen,
  onClose,
  onResultSelect,
  initialQuery = '',
  placeholder = 'Search for people, posts, pages, groups...',
  showFilters = true,
  showHistory = true,
  showTrending = true,
  categories = ['all', 'people', 'posts', 'pages', 'groups', 'events'],
  className = ''
}) => {
  const [query, setQuery] = useState(initialQuery);
  const [results, setResults] = useState<BaseSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [showFiltersPanel, setShowFiltersPanel] = useState(false);
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [trendingSearches, setTrendingSearches] = useState<string[]>([]);
  
  const [filters, setFilters] = useState<SearchFilters>({
    type: [],
    dateRange: 'all',
    location: '',
    sortBy: 'relevance',
    hasMedia: false,
    verified: false
  });

  // Load search history and trending searches
  useEffect(() => {
    if (isOpen) {
      const history = storage.get<string[]>('search_history', []);
      setSearchHistory(history.slice(0, 10)); // Show last 10 searches
      
      // Mock trending searches
      setTrendingSearches([
        'React development',
        'Photography tips',
        'Local events',
        'Food recipes',
        'Travel destinations',
        'Tech news',
        'Fitness routines',
        'Music festivals'
      ]);
    }
  }, [isOpen]);

  // Debounced search function
  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        const mockResults: BaseSearchResult[] = [
          {
            id: '1',
            type: 'user',
            title: 'Sarah Johnson',
            description: 'Software Engineer at Tech Corp',
            avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?w=400&h=400&fit=crop&crop=face',
            relevanceScore: 0.95,
            metadata: { verified: true, mutualFriends: 5 }
          },
          {
            id: '2',
            type: 'post',
            title: 'Amazing sunset photography',
            description: 'Check out this incredible sunset I captured yesterday...',
            thumbnail: 'https://images.pexels.com/photos/1640777/pexels-photo-1640777.jpeg?w=400&h=300&fit=crop',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            relevanceScore: 0.88,
            metadata: { likes: 45, comments: 12 }
          },
          {
            id: '3',
            type: 'group',
            title: 'React Developers Community',
            description: 'A community for React developers to share knowledge',
            avatar: 'https://images.pexels.com/photos/3182812/pexels-photo-3182812.jpeg?w=400&h=400&fit=crop',
            relevanceScore: 0.82,
            metadata: { members: 1250, privacy: 'public' }
          },
          {
            id: '4',
            type: 'page',
            title: 'Local Photography Studio',
            description: 'Professional photography services in your area',
            avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?w=400&h=400&fit=crop',
            location: 'New York, NY',
            relevanceScore: 0.75,
            metadata: { verified: true, rating: 4.8 }
          },
          {
            id: '5',
            type: 'event',
            title: 'Photography Workshop',
            description: 'Learn advanced photography techniques',
            thumbnail: 'https://images.pexels.com/photos/1640770/pexels-photo-1640770.jpeg?w=400&h=300&fit=crop',
            timestamp: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            location: 'Downtown Studio',
            relevanceScore: 0.70,
            metadata: { attendees: 25, price: 50 }
          },
          {
            id: '6',
            type: 'hashtag',
            title: '#photography',
            description: '2.5M posts',
            relevanceScore: 0.65,
            metadata: { postCount: 2500000, trending: true }
          }
        ].filter(result => {
          // Apply filters
          if (filters.type.length > 0 && !filters.type.includes(result.type)) return false;
          if (filters.verified && !result.metadata?.verified) return false;
          if (filters.hasMedia && !result.thumbnail) return false;
          return result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                 result.description?.toLowerCase().includes(searchQuery.toLowerCase());
        });

        // Sort results
        mockResults.sort((a, b) => {
          switch (filters.sortBy) {
            case 'recent':
              return new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime();
            case 'popular':
              return (b.metadata?.likes as number || 0) - (a.metadata?.likes as number || 0);
            default:
              return b.relevanceScore - a.relevanceScore;
          }
        });

        setResults(mockResults);
        setIsLoading(false);
      }, 500);
    }, 300),
    [filters]
  );

  // Handle search query change
  useEffect(() => {
    debouncedSearch(query);
  }, [query, debouncedSearch]);

  // Save search to history
  const saveToHistory = useCallback((searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    const history = storage.get<string[]>('search_history', []);
    const updatedHistory = [searchQuery, ...history.filter(h => h !== searchQuery)].slice(0, 20);
    storage.set('search_history', updatedHistory);
    setSearchHistory(updatedHistory.slice(0, 10));
  }, []);

  const handleResultClick = useCallback((result: BaseSearchResult) => {
    saveToHistory(query);
    onResultSelect(result);
    onClose();
  }, [query, onResultSelect, onClose, saveToHistory]);

  const handleHistoryClick = useCallback((historyQuery: string) => {
    setQuery(historyQuery);
  }, []);

  const clearHistory = useCallback(() => {
    storage.remove('search_history');
    setSearchHistory([]);
  }, []);

  const getResultIcon = (type: BaseSearchResult['type']) => {
    switch (type) {
      case 'user':
        return <Users className="w-4 h-4 text-blue-500" />;
      case 'group':
        return <Users className="w-4 h-4 text-green-500" />;
      case 'event':
        return <Calendar className="w-4 h-4 text-purple-500" />;
      case 'hashtag':
        return <Hash className="w-4 h-4 text-orange-500" />;
      default:
        return <Search className="w-4 h-4 text-gray-500" />;
    }
  };

  const filteredResults = useMemo(() => {
    if (activeTab === 'all') return results;
    return results.filter(result => {
      switch (activeTab) {
        case 'people':
          return result.type === 'user';
        case 'posts':
          return result.type === 'post';
        case 'pages':
          return result.type === 'page';
        case 'groups':
          return result.type === 'group';
        case 'events':
          return result.type === 'event';
        default:
          return true;
      }
    });
  }, [results, activeTab]);

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className={`absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 ${className}`}
    >
      <div className="p-4">
        {/* Search Input */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder={placeholder}
            className="pl-10 pr-12"
            autoFocus
          />
          {query && (
            <button
              onClick={() => setQuery('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              <X className="w-4 h-4" />
            </button>
          )}
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="flex items-center justify-between mb-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid grid-cols-6 w-full">
                {categories.map((category) => (
                  <TabsTrigger key={category} value={category} className="text-xs">
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFiltersPanel(!showFiltersPanel)}
              className="ml-2"
            >
              <Filter className="w-4 h-4" />
            </Button>
          </div>
        )}

        {/* Advanced Filters Panel */}
        {showFiltersPanel && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
          >
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">Sort by</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="w-full mt-1 text-xs p-2 border rounded"
                >
                  <option value="relevance">Relevance</option>
                  <option value="recent">Most Recent</option>
                  <option value="popular">Most Popular</option>
                </select>
              </div>
              <div>
                <label className="text-xs font-medium text-gray-700 dark:text-gray-300">Date Range</label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
                  className="w-full mt-1 text-xs p-2 border rounded"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                </select>
              </div>
            </div>
          </motion.div>
        )}

        {/* Search Results */}
        <div className="max-h-96 overflow-y-auto">
          {query ? (
            <div>
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
              ) : filteredResults.length > 0 ? (
                <div className="space-y-2">
                  <AnimatePresence>
                    {filteredResults.map((result) => (
                      <motion.div
                        key={result.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handleResultClick(result)}
                      >
                        <div className="flex items-center space-x-3">
                          {result.avatar || result.thumbnail ? (
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={result.avatar || result.thumbnail} />
                              <AvatarFallback>
                                {result.title.charAt(0)}
                              </AvatarFallback>
                            </Avatar>
                          ) : (
                            <div className="w-10 h-10 bg-gray-100 dark:bg-gray-600 rounded-full flex items-center justify-center">
                              {getResultIcon(result.type)}
                            </div>
                          )}
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium text-gray-900 dark:text-white truncate">
                                {result.title}
                              </h4>
                              {result.metadata?.verified && (
                                <Badge variant="secondary" className="text-xs">
                                  Verified
                                </Badge>
                              )}
                            </div>
                            {result.description && (
                              <p className="text-sm text-gray-600 dark:text-gray-300 truncate">
                                {result.description}
                              </p>
                            )}
                            <div className="flex items-center space-x-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {result.type}
                              </Badge>
                              {result.location && (
                                <span className="text-xs text-gray-500 flex items-center">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {result.location}
                                </span>
                              )}
                              {result.timestamp && (
                                <span className="text-xs text-gray-500">
                                  {formatDistanceToNow(new Date(result.timestamp), { addSuffix: true })}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Search className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">No results found</p>
                  <p className="text-sm text-gray-400 dark:text-gray-500">
                    Try adjusting your search terms or filters
                  </p>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Search History */}
              {showHistory && searchHistory.length > 0 && (
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      Recent Searches
                    </h4>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearHistory}
                      className="text-xs text-gray-500"
                    >
                      Clear
                    </Button>
                  </div>
                  <div className="space-y-1">
                    {searchHistory.map((historyItem, index) => (
                      <div
                        key={index}
                        className="p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer text-sm text-gray-600 dark:text-gray-300"
                        onClick={() => handleHistoryClick(historyItem)}
                      >
                        {historyItem}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Trending Searches */}
              {showTrending && trendingSearches.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 flex items-center">
                    <TrendingUp className="w-4 h-4 mr-1" />
                    Trending
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {trendingSearches.slice(0, 6).map((trending, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => setQuery(trending)}
                      >
                        {trending}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
});

UnifiedSearch.displayName = 'UnifiedSearch';

export default UnifiedSearch;
