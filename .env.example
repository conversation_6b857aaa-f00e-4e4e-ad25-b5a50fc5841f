# Environment Variables Template
# Copy this file to .env.local and update the values

# Application Environment
NODE_ENV=development

# API Configuration
VITE_API_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:8080

# WebSocket Configuration
VITE_USE_WEBSOCKET=false

# Supabase Configuration (if using Supabase)
VITE_SUPABASE_URL=your_supabase_url_here
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key_here

# Analytics & Monitoring
VITE_ENABLE_ANALYTICS=false
VITE_DEBUG_PERFORMANCE=true

# Feature Flags
VITE_ENABLE_VIDEO_CALLS=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_MARKETPLACE=true

# Development Settings
VITE_MOCK_DATA=true
VITE_ENABLE_DEVTOOLS=true
