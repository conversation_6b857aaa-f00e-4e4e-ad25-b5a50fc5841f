import React from 'react';
import LoadingSpinner from './LoadingSpinner';

// Base fallback for all routes
export const BaseSuspenseFallback = ({ message = "Loading..." }: { message?: string }) => (
  <div className="min-h-[80vh] flex items-center justify-center bg-gray-50 dark:bg-gray-900">
    <div className="text-center space-y-4">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-t-blue-500 border-blue-200 dark:border-blue-800 rounded-full animate-spin mx-auto"></div>
        <div className="absolute inset-0 w-8 h-8 border-2 border-t-blue-300 border-blue-100 dark:border-blue-600 rounded-full animate-spin-reverse mx-auto mt-2 ml-2"></div>
      </div>
      <div className="space-y-2">
        <div className="text-sm text-gray-600 dark:text-gray-300 font-medium">
          {message}
        </div>
        <div className="text-xs text-gray-500 dark:text-gray-400">
          Please wait while we prepare your content
        </div>
      </div>
    </div>
  </div>
);

// Home page specific fallback with news feed skeleton
export const HomeSuspenseFallback = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
    {/* Header skeleton */}
    <div className="h-14 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4">
      <div className="flex items-center space-x-4">
        <div className="w-8 h-8 bg-blue-500 rounded"></div>
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 animate-pulse"></div>
      </div>
      <div className="ml-auto flex items-center space-x-2">
        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
      </div>
    </div>

    <div className="flex">
      {/* Left sidebar skeleton */}
      <div className="hidden md:block w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-4">
        <div className="space-y-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded flex-1 animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="flex-1 p-4">
        <div className="max-w-2xl mx-auto space-y-4">
          {/* Create post skeleton */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded flex-1 animate-pulse"></div>
            </div>
          </div>

          {/* Stories skeleton */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="flex space-x-3 overflow-x-hidden">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex-shrink-0">
                  <div className="w-20 h-28 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>

          {/* Posts skeleton */}
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4 animate-pulse"></div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse"></div>
                <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Right sidebar skeleton */}
      <div className="hidden lg:block w-80 bg-gray-50 dark:bg-gray-900 p-4">
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3 animate-pulse"></div>
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded flex-1 animate-pulse"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Loading indicator */}
    <div className="fixed bottom-4 right-4">
      <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg">
        <LoadingSpinner size="small" />
      </div>
    </div>
  </div>
);

// Messages page specific fallback
export const MessagesSuspenseFallback = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex">
    {/* Conversations list skeleton */}
    <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
      <div className="p-4">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4 animate-pulse"></div>
        <div className="space-y-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3 p-2">
              <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Chat area skeleton */}
    <div className="flex-1 flex flex-col">
      {/* Chat header */}
      <div className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center px-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32 animate-pulse"></div>
        </div>
      </div>

      {/* Messages area */}
      <div className="flex-1 p-4">
        <div className="text-center">
          <LoadingSpinner size="large" />
          <p className="mt-4 text-gray-600 dark:text-gray-300">Loading conversation...</p>
        </div>
      </div>

      {/* Message input skeleton */}
      <div className="h-16 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
        <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
      </div>
    </div>
  </div>
);

// Watch page specific fallback
export const WatchSuspenseFallback = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
    <div className="flex">
      {/* Video area skeleton */}
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto space-y-4">
          {/* Video player skeleton */}
          <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          
          {/* Video info skeleton */}
          <div className="space-y-3">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/4 animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Suggested videos skeleton */}
      <div className="w-80 p-4">
        <div className="space-y-4">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="flex space-x-3">
              <div className="w-32 h-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Loading indicator */}
    <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <div className="text-center">
        <LoadingSpinner size="large" />
        <p className="mt-4 text-gray-600 dark:text-gray-300">Loading video...</p>
      </div>
    </div>
  </div>
);

// Profile page specific fallback
export const ProfileSuspenseFallback = () => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
    {/* Cover photo skeleton */}
    <div className="h-80 bg-gray-200 dark:bg-gray-700 animate-pulse"></div>
    
    {/* Profile info skeleton */}
    <div className="max-w-4xl mx-auto px-4 -mt-20">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-end space-x-4">
          <div className="w-32 h-32 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse"></div>
          <div className="flex-1 space-y-3">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 animate-pulse"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>

    {/* Content skeleton */}
    <div className="max-w-4xl mx-auto p-4 mt-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3 animate-pulse"></div>
              <div className="h-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            </div>
          ))}
        </div>
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-3 animate-pulse"></div>
            <div className="space-y-2">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>

    {/* Loading indicator */}
    <div className="fixed bottom-4 right-4">
      <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg">
        <LoadingSpinner size="small" />
      </div>
    </div>
  </div>
);

// Generic page fallback for simpler pages
export const PageSuspenseFallback = ({ title }: { title: string }) => (
  <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4">
    <div className="max-w-4xl mx-auto">
      <div className="text-center py-20">
        <LoadingSpinner size="large" />
        <h2 className="mt-6 text-xl font-semibold text-gray-900 dark:text-gray-100">
          Loading {title}
        </h2>
        <p className="mt-2 text-gray-600 dark:text-gray-300">
          Please wait while we prepare your {title.toLowerCase()} content
        </p>
        
        {/* Skeleton content */}
        <div className="mt-12 space-y-6">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow">
                <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded mb-3 animate-pulse"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2 animate-pulse"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default {
  BaseSuspenseFallback,
  HomeSuspenseFallback,
  MessagesSuspenseFallback,
  WatchSuspenseFallback,
  ProfileSuspenseFallback,
  PageSuspenseFallback,
};
