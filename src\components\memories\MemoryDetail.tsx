import React, { useState } from 'react';
import { Heart, MessageCircle, Share, Camera, Clock, MapPin, ChevronLeft, ChevronRight, Users, AlertCircle } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from 'sonner';
import { MOCK_IMAGES } from '@/lib/constants';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';

interface Memory {
  id: string;
  type: 'photo' | 'post' | 'event' | 'friendship';
  title: string;
  date: Date;
  yearsAgo: number;
  images: string[];
  content?: string;
  location?: string;
  peopleTagged?: {
    id: string;
    name: string;
    avatar: string;
  }[];
  interactions: {
    likes: number;
    comments: number;
    shares: number;
  };
  isLiked?: boolean;
  comments?: {
    id: string;
    author: {
      name: string;
      avatar: string;
    };
    content: string;
    timestamp: string;
  }[];
}

interface MemoryDetailProps {
  memory: Memory | null;
  isOpen: boolean;
  onClose: () => void;
}

const MemoryDetail: React.FC<MemoryDetailProps> = ({
  memory,
  isOpen,
  onClose
}) => {
  const [newComment, setNewComment] = useState('');
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleShare = (memory: Memory) => {
    toast.success(`Shared memory: ${memory.title}`);
  };

  const handleLike = (_memoryId: string) => {
    toast.success('Memory reaction updated!');
  };

  const handleAddComment = () => {
    if (!newComment.trim() || !memory) return;
    
    setNewComment('');
    toast.success('Comment added!');
  };

  const handlePrevSlide = () => {
    if (!memory) return;
    setCurrentSlide(prev => (prev > 0 ? prev - 1 : memory.images.length - 1));
  };

  const handleNextSlide = () => {
    if (!memory) return;
    setCurrentSlide(prev => (prev < memory.images.length - 1 ? prev + 1 : 0));
  };

  const getMemoryIcon = (type: string) => {
    switch (type) {
      case 'photo': return <Camera className="w-5 h-5 text-blue-500" />;
      case 'event': return <Clock className="w-5 h-5 text-green-500" />;
      case 'friendship': return <Users className="w-5 h-5 text-purple-500" />;
      default: return <MessageCircle className="w-5 h-5 text-purple-500" />;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden p-0">
        <DialogHeader className="p-4 border-b dark:border-gray-700">
          <DialogTitle className="flex items-center space-x-2">
            {memory && getMemoryIcon(memory.type)}
            <span>{memory?.title || 'Memory'}</span>
          </DialogTitle>
        </DialogHeader>
        
        {memory && (
          <div className="grid grid-cols-1 md:grid-cols-2 h-[80vh]">
            {/* Image Viewer */}
            <div className="relative bg-black flex items-center justify-center">
              {memory.images.length > 0 ? (
                <>
                  <img 
                    src={memory.images[currentSlide]} 
                    alt={`Memory ${currentSlide + 1}`}
                    className="max-h-full max-w-full object-contain"
                  />
                  
                  {/* Navigation arrows */}
                  {memory.images.length > 1 && (
                    <>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handlePrevSlide}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white hover:bg-black/70 h-10 w-10 rounded-full p-0"
                      >
                        <ChevronLeft className="h-6 w-6" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleNextSlide}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black/50 text-white hover:bg-black/70 h-10 w-10 rounded-full p-0"
                      >
                        <ChevronRight className="h-6 w-6" />
                      </Button>
                      
                      {/* Slide indicators */}
                      <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                        {memory.images.map((_, index) => (
                          <div 
                            key={index} 
                            className={`w-2 h-2 rounded-full ${
                              index === currentSlide ? 'bg-white' : 'bg-white/50'
                            }`}
                          />
                        ))}
                      </div>
                    </>
                  )}
                </>
              ) : (
                <div className="flex flex-col items-center justify-center text-white p-6">
                  <AlertCircle className="w-16 h-16 mb-4 opacity-50" />
                  <p className="text-center">No images available for this memory</p>
                </div>
              )}
            </div>
            
            {/* Memory Details & Comments */}
            <div className="flex flex-col h-full bg-white dark:bg-gray-800">
              {/* Memory Info */}
              <div className="p-4 border-b dark:border-gray-700">
                <div className="flex items-center space-x-3 mb-3">
                  <Avatar className="w-10 h-10">
                    <AvatarImage src={MOCK_IMAGES.AVATARS[7]} />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold dark:text-white">Your Memory</p>
                    <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                      <Clock className="w-4 h-4" />
                      <span>{format(memory.date, 'MMMM d, yyyy')} • {memory.yearsAgo} {memory.yearsAgo === 1 ? 'year' : 'years'} ago</span>
                    </div>
                  </div>
                </div>
                
                <p className="text-gray-900 mb-3 dark:text-white">{memory.content}</p>
                
                {/* Location */}
                {memory.location && (
                  <div className="flex items-center space-x-1 text-sm text-gray-600 mb-3 dark:text-gray-300">
                    <MapPin className="w-4 h-4" />
                    <span>{memory.location}</span>
                  </div>
                )}
                
                {/* Tagged People */}
                {memory.peopleTagged && memory.peopleTagged.length > 0 && (
                  <div className="mb-3">
                    <p className="text-sm text-gray-600 mb-2 dark:text-gray-300">With:</p>
                    <div className="flex flex-wrap gap-2">
                      {memory.peopleTagged.map((person) => (
                        <div key={person.id} className="flex items-center space-x-1 bg-gray-100 rounded-full px-2 py-1 dark:bg-gray-700">
                          <Avatar className="w-5 h-5">
                            <AvatarImage src={person.avatar} />
                            <AvatarFallback>{person.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-gray-700 dark:text-gray-300">{person.name}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Interactions */}
                <div className="flex items-center justify-between pt-3 border-t dark:border-gray-700">
                  <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                    <span>{memory.interactions.likes} likes</span>
                    <span>{memory.interactions.comments} comments</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleLike(memory.id)}
                      className={`text-gray-600 hover:text-red-600 dark:text-gray-300 dark:hover:text-red-400 ${
                        memory.isLiked ? 'text-red-600 dark:text-red-400' : ''
                      }`}
                    >
                      <Heart className={`w-4 h-4 ${memory.isLiked ? 'fill-current' : ''}`} />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleShare(memory)}
                      className="text-gray-600 hover:text-green-600 dark:text-gray-300 dark:hover:text-green-400"
                    >
                      <Share className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              {/* Comments */}
              <div className="flex-1 overflow-y-auto p-4">
                <h3 className="font-semibold mb-4 dark:text-white">Comments</h3>
                
                <div className="space-y-4">
                  <AnimatePresence>
                    {memory.comments && memory.comments.length > 0 ? (
                      memory.comments.map((comment) => (
                        <motion.div 
                          key={comment.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0 }}
                          className="flex space-x-3"
                        >
                          <Avatar className="w-8 h-8">
                            <AvatarImage src={comment.author.avatar} />
                            <AvatarFallback>{comment.author.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <div className="bg-gray-100 rounded-lg px-3 py-2 dark:bg-gray-700">
                              <p className="font-medium text-sm dark:text-white">{comment.author.name}</p>
                              <p className="text-sm dark:text-gray-200">{comment.content}</p>
                            </div>
                            <div className="flex items-center mt-1 space-x-4">
                              <span className="text-xs text-gray-500 dark:text-gray-400">{comment.timestamp}</span>
                              <button className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                Like
                              </button>
                              <button className="text-xs font-medium text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                                Reply
                              </button>
                            </div>
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <p className="text-center text-gray-500 dark:text-gray-400">No comments yet. Be the first to comment!</p>
                    )}
                  </AnimatePresence>
                </div>
              </div>
              
              {/* Add Comment */}
              <div className="p-4 border-t dark:border-gray-700">
                <div className="flex space-x-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={MOCK_IMAGES.AVATARS[7]} />
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 flex space-x-2">
                    <input
                      type="text"
                      placeholder="Write a comment..."
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      className="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                      onKeyPress={(e) => e.key === 'Enter' && handleAddComment()}
                    />
                    <Button 
                      size="sm" 
                      onClick={handleAddComment}
                      disabled={!newComment.trim()}
                    >
                      Post
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default MemoryDetail;
